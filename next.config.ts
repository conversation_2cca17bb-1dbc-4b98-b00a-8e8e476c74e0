import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  experimental: {
    serverActions: {
      bodySizeLimit: "20mb",
    },
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "plus.unsplash.com",
      },{
        protocol: "https",
        hostname: "sdbooth2-production.s3.amazonaws.com",
      },{
        protocol: "https",
        hostname: "api.astria.ai",
      }
    ],
  },
};

export default nextConfig;
