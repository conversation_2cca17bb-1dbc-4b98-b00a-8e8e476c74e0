"use server";

import { AstriaPacksService } from "@/lib/astria-packs";

export async function getPacksOnserver(packId: string) {
  const astriaPacksService = new AstriaPacksService();
  const pack = await astriaPacksService.getPackById(Number(packId));

  const combinedPrompts = Object.keys(pack.data?.prompts_per_class || {})
    .map((key) => {
      return pack.data?.prompts_per_class[
        key as keyof typeof pack.data.prompts_per_class
      ]?.filter((item) => item.id !== undefined);
    })
    .flat()
    .filter((item): item is NonNullable<typeof item> => Boolean(item));

  return combinedPrompts;
}
