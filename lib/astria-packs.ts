import axios from "axios";
import type {
  AstriaPack,
  AstriaPacksResponse,
  AstriaPacksQueryParams,
  AstriaPackByIdResponse,
  AstriaPackByIdTypes,
} from "@/types/astria";

const ASTRIA_API_BASE_URL = "https://api.astria.ai";

/**
 * Service class for interacting with Astria AI Packs API
 */
export class AstriaPacksService {
  private apiKey: string;

  constructor(apiKey?: string) {
    this.apiKey = apiKey || process.env.NEXT_PUBLIC_ASTRIA_API_KEY || "";

    if (!this.apiKey) {
      throw new Error("Astria API key is required");
    }
  }

  /**
   * Fetch all packs from Astria AI
   * @param params Query parameters for filtering packs
   * @returns Promise with packs data
   */
  async getAllPacks(
    params: AstriaPacksQueryParams = {}
  ): Promise<AstriaPacksResponse> {
    try {
      // Determine which endpoint to use
      const endpoint = params.gallery ? "/gallery/packs" : "/packs";
      const url = `${ASTRIA_API_BASE_URL}${endpoint}`;

      // Build query string
      const queryParams = new URLSearchParams();
      if (params.public) {
        queryParams.append("public", "true");
      }
      if (params.listed) {
        queryParams.append("listed", "true");
      }

      const finalUrl = queryParams.toString()
        ? `${url}?${queryParams.toString()}`
        : url;

      // Make request to Astria API
      const response = await axios.get<AstriaPack[]>(finalUrl, {
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          "Content-Type": "application/json",
        },
        timeout: 30000,
      });

      return {
        success: true,
        message: `Successfully fetched ${response.data.length} packs`,
        data: response.data,
      };
    } catch (error: any) {
      console.error("Error fetching Astria packs:", error);

      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.message || error.message;
        return {
          success: false,
          message: `Astria API error: ${errorMessage}`,
          error: errorMessage,
        };
      }

      return {
        success: false,
        message: "Error fetching packs",
        error: error.message || "Unknown error",
      };
    }
  }

  /**
   * Fetch a pack by ID from Astria AI
   * @param packId The pack ID
   * @returns Promise with pack data
   */

  async getPackById(packId: number): Promise<AstriaPackByIdResponse> {
    console.log("Fetching pack", packId, this.apiKey);
    try {
      const url = `${ASTRIA_API_BASE_URL}/p/${packId}`;
      const response = await axios.get<AstriaPackByIdTypes>(url, {
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
          "Content-Type": "application/json",
        },
      });

      return {
        success: true,
        message: "Successfully fetched pack",
        data: response.data,
      };
    } catch (error: any) {
      throw new Error(error.message);
      // console.error("Error fetching Astria pack:", error);

      // if (axios.isAxiosError(error)) {
      //   const errorMessage = error.response?.data?.message || error.message;
      //   return {
      //     success: false,
      //     message: `Astria API error: ${errorMessage}`,
      //     error: errorMessage,
      //   };
      // }

      // return {
      //   success: false,
      //   message: "Error fetching packs",
      //   error: error.message || "Unknown error",
      // };
    }
  }

  /**
   * Fetch gallery packs (public packs)
   * @param params Optional query parameters
   * @returns Promise with gallery packs data
   */
  async getGalleryPacks(
    params: Omit<AstriaPacksQueryParams, "gallery"> = {}
  ): Promise<AstriaPacksResponse> {
    return this.getAllPacks({ ...params, gallery: true });
  }

  /**
   * Fetch user's own packs
   * @param params Optional query parameters
   * @returns Promise with user packs data
   */
  async getUserPacks(
    params: Omit<AstriaPacksQueryParams, "gallery"> = {}
  ): Promise<AstriaPacksResponse> {
    return this.getAllPacks({ ...params, gallery: false });
  }

  /**
   * Fetch public packs only
   * @param gallery Whether to fetch from gallery or user packs
   * @returns Promise with public packs data
   */
  async getPublicPacks(gallery: boolean = true): Promise<AstriaPacksResponse> {
    return this.getAllPacks({ public: true, gallery });
  }

  /**
   * Fetch listed packs only (not unlisted)
   * @param gallery Whether to fetch from gallery or user packs
   * @returns Promise with listed packs data
   */
  async getListedPacks(gallery: boolean = true): Promise<AstriaPacksResponse> {
    return this.getAllPacks({ listed: true, gallery });
  }
}
