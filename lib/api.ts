// Firebase functions have been moved to lib/firebase
// Import them directly from there using:
// import { login, register, etc. } from '@/lib/firebase';

import {
  createModelCreationFormData,
  createImageGenerationFormData,
  createVideoGenerationFormData,
  createUpscaleFormData
} from './form-utils';

/**
 * Fine-tune a new model on Astria
 * @param user  - The user ID (string)
 * @param title - The model title (string)
 * @param images - Array of File objects (the training images)
 * @param modelType - Type of model to create (lora or faceid)
 * @param enhancementType - Type of enhancement (headshot, product, food, virtual try-on)
 * @param className - Class name for faceid models (e.g., man, woman, cat, dog)
 * @returns { success: boolean, data?: { tuneId: string }, message?: string }
 */
export async function finetuneModelOnAstria({
  user,
  title,
  images,
  modelType = "lora",
  enhancementType,
  className,
}: {
  user: string;
  title: string;
  images: File[];
  modelType?: "lora" | "faceid";
  enhancementType: string;
  className?: string;
}) {
  try {
    const formData = createModelCreationFormData({
      user,
      title,
      images,
      modelType,
      enhancementType,
      className,
    });

    const createRes = await fetch("/api/proxy/create-tune", {
      method: "POST",
      body: formData,
    });

    if (!createRes.ok) {
      const errorText = await createRes.text();
      return { success: false, message: errorText };
    }

    const result = await createRes.json();
    // Handle both the new response format and maintain backward compatibility
    if (result.success) {
      return {
        success: true,
        data: { tuneId: result.tuneId, details: result.details },
      };
    } else {
      return { success: true, data: { tuneId: result.tuneId } };
    }
  } catch (error: any) {
    return {
      success: false,
      message: error.message || "Unknown error occurred",
    };
  }
}

/**
 * Generate images from an existing Astria model
 * @param user   - The user ID (string)
 * @param tuneId - The Astria model ID (string)
 * @param prompt - The text prompt (string)
 * @param enhancementType - The type of enhancement (headshot, product, food, virtual try-on, interior, etc.)
 * @param negativePrompt - [REMOVED: Not supported on Flux]
 * @param inputImage - FormData containing the input image for img2img (optional)
 * @param controlnet - ControlNet type to use (optional)
 * @param denoisingStrength - Denoising strength for img2img (optional)
 * @param style - Style to apply to the generated image (optional)
 * @param garmentModelId - ID of the garment model for virtual try-on (optional)
 * @param controlnets - Array of ControlNet models to use (optional, for interior design)
 * @param controlnetWeights - Array of weights for each ControlNet model (optional, for interior design)
 * @param maskPrompt - Prompt for masking specific elements (optional, for interior design)
 * @param maskInvert - Whether to invert the mask (optional, for interior design)
 * @returns { success: boolean, data?: string[], message?: string }
 */
export async function generateImagesOnAstria({
  user,
  tuneId,
  prompt,
  enhancementType,
  // Removed negative prompt parameter as it's not supported on Flux
  inputImage,
  controlnet,
  denoisingStrength,
  style,
  garmentModelId,
  controlnets,
  controlnetWeights,
  maskPrompt,
  maskInvert,
  superResolution,
  hiresFixEnabled,
  tiledUpscale,
  tiledUpscaleVersion,
  hiresCfgScale,
  resemblance,
}: {
  user: string;
  tuneId: string;
  prompt: string;
  enhancementType?: string;
  // Removed negative prompt parameter as it's not supported on Flux
  inputImage?: FormData | null;
  controlnet?: string | null;
  denoisingStrength?: number;
  style?: string;
  garmentModelId?: string;
  controlnets?: string[];
  controlnetWeights?: number[];
  maskPrompt?: string;
  maskInvert?: boolean;
  superResolution?: boolean;
  hiresFixEnabled?: boolean;
  tiledUpscale?: boolean;
  tiledUpscaleVersion?: "v1" | "v2";
  hiresCfgScale?: number;
  resemblance?: number;
}) {
  try {
    console.log(tuneId);

    // Check tune status
    while (true) {
      const tuneRes = await fetch(`/api/proxy/tune-status/${tuneId}`);
      const tuneStatus = await tuneRes.json();
      if (tuneStatus.expires_at !== null) break;
      await new Promise((resolve) => setTimeout(resolve, 60000));
    }

    // Create prompt using centralized form utility
    let inputImageFile: File | null = null;
    if (inputImage) {
      inputImageFile = inputImage.get("image") as File;
    }

    const formData = createImageGenerationFormData({
      user,
      tuneId,
      prompt,
      enhancementType,
      inputImage: inputImageFile,
      controlnet,
      denoisingStrength,
      style,
      garmentModelId,
      controlnets,
      controlnetWeights,
      maskPrompt,
      maskInvert,
      superResolution,
      hiresFixEnabled,
      tiledUpscale,
      tiledUpscaleVersion,
      hiresCfgScale,
      resemblance,
    });

    // Handle special case for interior design controlnets formatting
    if (controlnets && controlnets.length > 0) {
      formData.set("controlnets", controlnets.join(" "));

      if (controlnetWeights && controlnetWeights.length === controlnets.length) {
        formData.set("controlnet_weights", controlnetWeights.join(" "));
      }
    }

    // Handle tiled upscale flag in prompt text (special case)
    if (tiledUpscale) {
      const tiledUpscaleFlag =
        tiledUpscaleVersion === "v2" ? "--tiled_upscale_v2" : "--tiled_upscale";
      const currentPrompt = formData.get("prompt") as string;
      formData.set("prompt", `${currentPrompt} ${tiledUpscaleFlag}`);
    }

    const promptRes = await fetch(`/api/proxy/create-prompt/${tuneId}`, {
      method: "POST",
      body: formData,
    });

    if (!promptRes.ok) {
      const errorText = await promptRes.text();
      return { success: false, message: errorText };
    }

    const promptData = await promptRes.json();
    const promptId = promptData.id;

    // Poll for results
    while (true) {
      const resultRes = await fetch(`/api/proxy/prompt-result/${promptId}`);
      const resultData = await resultRes.json();
      if (resultData.images && resultData.images.length > 0) {
        return { success: true, data: resultData.images };
      }
      await new Promise((resolve) => setTimeout(resolve, 5000));
    }
  } catch (error: any) {
    return {
      success: false,
      message: error.message || "Unknown error occurred",
    };
  }
}

/**
 * Upscale an image using Astria's upscaling capabilities
 * @param user - The user ID (string)
 * @param inputImage - FormData containing the input image to upscale
 * @param options - Upscaling options
 * @returns { success: boolean, data?: string[], message?: string }
 */
/**
 * Generate video from an image using Astria's image-to-video capabilities
 * @param user - The user ID (string)
 * @param tuneId - The Astria model ID (string)
 * @param prompt - The text prompt (string)
 * @param videoPrompt - Optional specific prompt for the video generation
 * @param videoModel - Optional video resolution model (720p or 480p)
 * @param inputImage - FormData containing the input image for img2img (optional)
 * @param controlnet - ControlNet type to use (optional)
 * @param denoisingStrength - Denoising strength for img2img (optional)
 * @param style - Style to apply to the generated image (optional)
 * @returns { success: boolean, data?: { imageUrl: string, videoUrl: string }, message?: string }
 */
export async function generateVideoOnAstria({
  user,
  tuneId,
  prompt,
  videoPrompt,
  videoModel,
  inputImage,
  controlnet,
  denoisingStrength,
  style,
}: {
  user: string;
  tuneId: string;
  prompt: string;
  videoPrompt?: string;
  videoModel?: "720p" | "480p";
  inputImage?: FormData | null;
  controlnet?: string | null;
  denoisingStrength?: number;
  style?: string;
}) {
  try {
    // Create FormData using centralized utility
    let inputImageFile: File | null = null;
    if (inputImage) {
      inputImageFile = inputImage.get("image") as File;
    }

    const formData = createVideoGenerationFormData({
      user,
      tuneId,
      prompt,
      videoPrompt,
      videoModel,
      inputImage: inputImageFile,
      controlnet,
      denoisingStrength,
      style,
    });

    // Call the video generation API
    const videoRes = await fetch(`/api/proxy/generate-video`, {
      method: "POST",
      body: formData,
    });

    if (!videoRes.ok) {
      const errorText = await videoRes.text();
      return { success: false, message: errorText };
    }

    const videoData = await videoRes.json();
    const promptId = videoData.id;

    // Poll for results
    while (true) {
      const resultRes = await fetch(`/api/proxy/prompt-result/${promptId}`);
      const resultData = await resultRes.json();

      // Check if we have both image and video results
      if (
        resultData.images &&
        resultData.images.length > 0 &&
        resultData.videos &&
        resultData.videos.length > 0
      ) {
        return {
          success: true,
          data: {
            imageUrl: resultData.images[0],
            videoUrl: resultData.videos[0],
          },
        };
      }

      // If we only have images but no videos yet, keep polling
      if (
        resultData.images &&
        resultData.images.length > 0 &&
        (!resultData.videos || resultData.videos.length === 0)
      ) {
        // Wait a bit longer for video processing
        await new Promise((resolve) => setTimeout(resolve, 10000));
        continue;
      }

      // Standard polling interval
      await new Promise((resolve) => setTimeout(resolve, 5000));
    }
  } catch (error: any) {
    return {
      success: false,
      message: error.message || "Unknown error occurred",
    };
  }
}

export async function upscaleImage({
  user,
  inputImage,
  options,
}: {
  user: string;
  inputImage: FormData;
  options: {
    superResolution: boolean;
    hiresFixEnabled: boolean;
    tiledUpscale: boolean;
    tiledUpscaleVersion: "v1" | "v2";
    hiresCfgScale: number;
    resemblance: number;
    denoisingStrength: number;
    useModelTraining?: boolean;
    modelId?: string | null;
    focusPrompt?: string;
  };
}) {
  try {
    // Get the image file from the FormData
    const imageFile = inputImage.get("image") as File;

    // Build prompt based on model training option
    let prompt: string = "";
    if (options.useModelTraining) {
      prompt = `<lora:${options.modelId}:1.0> ohwx ${user?.toString().toLowerCase()} ${options.focusPrompt || ""}`;
    } else {
      prompt = `${options.focusPrompt || ""}`;
    }

    if (options.tiledUpscale) {
      const tiledUpscaleFlag = `${
        options.tiledUpscaleVersion === "v2"
          ? "--tiled_upscale_v2"
          : "--tiled_upscale"
      }`;
      prompt += ` ${tiledUpscaleFlag}`;
    }

    // Create FormData using centralized utility
    const formData = createUpscaleFormData({
      user,
      prompt,
      inputImage: imageFile,
      denoisingStrength: options.denoisingStrength,
      modelId: options.modelId as string,
      superResolution: options.superResolution,
      hiresFixEnabled: options.hiresFixEnabled,
      hiresCfgScale: options.hiresCfgScale.toString(),
      resemblance: options.resemblance.toString(),
    });

    // Use the provided model ID if model training is enabled, otherwise use default
    const apiEndpoint = `/api/proxy/upscale-image`;

    const upscaleRes = await fetch(apiEndpoint, {
      method: "POST",
      body: formData,
    });

    if (!upscaleRes.ok) {
      const errorText = await upscaleRes.text();
      return { success: false, message: errorText };
    }

    const upscaleData = await upscaleRes.json();
    const promptId = upscaleData.id;

    // Poll for results
    while (true) {
      const resultRes = await fetch(`/api/proxy/prompt-result/${promptId}`);
      const resultData = await resultRes.json();
      if (resultData.images && resultData.images.length > 0) {
        return { success: true, data: resultData.images };
      }
      await new Promise((resolve) => setTimeout(resolve, 5000));
    }
  } catch (error: any) {
    return {
      success: false,
      message: error.message || "Unknown error occurred",
    };
  }
}
