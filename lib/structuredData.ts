// Structured data for the homepage
export const homePageSchema = {
  '@context': 'https://schema.org',
  '@type': 'WebSite',
  name: 'Burst Mode AI',
  url: 'https://burstmode.ai',
  potentialAction: {
    '@type': 'SearchAction',
    target: 'https://burstmode.ai/search?q={search_term_string}',
    'query-input': 'required name=search_term_string',
  },
};

// Structured data for the organization
export const organizationSchema = {
  '@context': 'https://schema.org',
  '@type': 'Organization',
  name: 'Burst Mode AI',
  url: 'https://burstmode.ai',
  logo: 'https://burstmode.ai/app/icon.png',
  sameAs: [
    'https://twitter.com/burstmodeai',
    'https://facebook.com/burstmodeai',
    'https://instagram.com/burstmodeai',
    'https://linkedin.com/company/burstmodeai',
  ],
};

// Structured data for product pages
export const productSchema = (name: string, description: string, price: string, image: string) => ({
  '@context': 'https://schema.org',
  '@type': 'SoftwareApplication',
  name,
  description,
  applicationCategory: 'Photography',
  operatingSystem: 'Web',
  offers: {
    '@type': 'Offer',
    price,
    priceCurrency: 'USD',
  },
  image,
});

// Structured data for FAQ pages
export const faqSchema = (questions: { question: string; answer: string }[]) => ({
  '@context': 'https://schema.org',
  '@type': 'FAQPage',
  mainEntity: questions.map((q) => ({
    '@type': 'Question',
    name: q.question,
    acceptedAnswer: {
      '@type': 'Answer',
      text: q.answer,
    },
  })),
});

// Structured data for articles/blog posts
export const articleSchema = (
  title: string,
  description: string,
  image: string,
  publishDate: string,
  modifiedDate: string,
  authorName: string
) => ({
  '@context': 'https://schema.org',
  '@type': 'Article',
  headline: title,
  description,
  image,
  datePublished: publishDate,
  dateModified: modifiedDate,
  author: {
    '@type': 'Person',
    name: authorName,
  },
  publisher: {
    '@type': 'Organization',
    name: 'Burst Mode AI',
    logo: {
      '@type': 'ImageObject',
      url: 'https://burstmode.ai/app/icon.png',
    },
  },
});
