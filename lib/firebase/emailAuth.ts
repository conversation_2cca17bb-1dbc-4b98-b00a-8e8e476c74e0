import { auth, db } from './config';
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  sendEmailVerification,
  setPersistence,
  browserSessionPersistence,
  browserLocalPersistence,
  User
} from "firebase/auth";
import { doc, setDoc, getDoc } from "firebase/firestore";

/**
 * Signup using Firebase
 * @param email - The user's email (string)
 * @param fullname - The user's fullname (string)
 * @param password - The password entered by the user (string)
 * @returns { success: boolean, message?: string }
 */
export const register = async (
  email: string,
  fullname: string,
  password: string
): Promise<{ success: boolean; message: string }> => {
  try {
    const userCredential = await createUserWithEmailAndPassword(
      auth,
      email,
      password
    );

    const user = userCredential.user;

    await setDoc(doc(db, "users", user.uid), { fullname, email });

    try {
      await sendEmailVerification(user);
    } catch (error: any) {
      return {
        success: false,
        message: error.message
          ? error.message
          : "Failed to send verification email",
      };
    }

    return { success: true, message: "Registration successful" };
  } catch (error: any) {
    let errorMessage = "Registration failed";

    if (error.code) {
      switch (error.code) {
        case "auth/email-already-in-use":
          errorMessage =
            "The email address is already in use by another account.";
          break;
        case "auth/invalid-email":
          errorMessage = "The email address is not valid.";
          break;
        case "auth/operation-not-allowed":
          errorMessage =
            "Email/password accounts are not enabled. Please contact support.";
          break;
        case "auth/weak-password":
          errorMessage =
            "The password is too weak. It should be at least 6 characters long.";
          break;
        default:
          errorMessage = error.message ? error.message : errorMessage;
          break;
      }
    }

    return {
      success: false,
      message: errorMessage,
    };
  }
};

/**
 * Login using Firebase Authentication
 * @param email - The user's email (string)
 * @param password - The password entered by the user (string)
 * @param rememberMe - Boolean indicating if the session should persist across browser restarts
 * @returns { success: boolean; verified: boolean; user?: { email: string | null; fullname: string | null }; message: string }
 */
export const login = async (
  email: string,
  password: string,
  rememberMe: boolean
): Promise<{
  success: boolean;
  verified: boolean;
  user?: User;
  message: string;
}> => {
  try {
    // Set persistence based on the "Remember Me" option
    const persistence = rememberMe
      ? browserLocalPersistence
      : browserSessionPersistence;
    await setPersistence(auth, persistence);

    // Sign in the user
    const userCredential = await signInWithEmailAndPassword(
      auth,
      email,
      password
    );
    const user = userCredential.user;
    const token = await user.getIdTokenResult();
    console.log(token.claims);

    if (!user.emailVerified) {
      return {
        success: true,
        verified: false,
        user: user,
        message:
          "Email not verified. Please verify your email before logging in.",
      };
    }

    const userDoc = await getDoc(doc(db, "users", user.uid));
    const fullname = userDoc.exists() ? userDoc.data().fullname : null;

    return {
      success: true,
      verified: true,
      user: user,
      message: "Login successful.",
    };
  } catch (error: any) {
    let errorMessage = "Login failed";

    if (error.code) {
      switch (error.code) {
        case "auth/invalid-email":
          errorMessage = "The email address is not valid.";
          break;
        case "auth/user-disabled":
          errorMessage = "This user account has been disabled.";
          break;
        case "auth/user-not-found":
          errorMessage = "No user found with this email address.";
          break;
        case "auth/wrong-password":
          errorMessage = "Incorrect password.";
          break;
        default:
          errorMessage = error.message ? error.message : errorMessage;
          break;
      }
    }

    return {
      success: false,
      verified: false,
      user: undefined,
      message: errorMessage,
    };
  }
};
