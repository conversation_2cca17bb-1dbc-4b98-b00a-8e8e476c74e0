"use client";
import { createContext, useContext, useState } from "react";
import { AstriaPackImageTypes, PacksContextType } from "./context";
import { AstriaPacksService } from "@/lib/astria-packs";
import { getPacksOnserver } from "@/actions/getPacksOnserver";

const PacksContext = createContext<PacksContextType>({} as PacksContextType);

export const PacksProvider = ({ children }: { children: React.ReactNode }) => {
  const [packsPrompt, setPacksPrompt] = useState<AstriaPackImageTypes[]>([]);

  const getPacksPrompt = async (packId: string) => {
    getPacksOnserver(packId).then((data) => {
      setPacksPrompt(data);
    });
  };

  return (
    <PacksContext.Provider
      value={{ packsPrompt, setPacksPrompt, getPacksPrompt }}
    >
      {children}
    </PacksContext.Provider>
  );
};

export const usePacks = () => {
  const context = useContext(PacksContext);
  if (!context) {
    throw new Error("usePacks must be used within a PacksProvider");
  }
  return context;
};
