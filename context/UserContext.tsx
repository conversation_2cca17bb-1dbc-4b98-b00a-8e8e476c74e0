"use client";

import {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import {
  onAuthStateChanged,
  signOut,
  User as FirebaseUser
} from "firebase/auth";
import { doc, getDoc,getDocs, collection } from "firebase/firestore";
import { auth, db } from "@/lib/firebase";


// Define user type
interface User {
  uid: string;
  email: string | null;
  fullname: string | null;
  planName: string | null;
  planCredits: number | null;
  planImageCredits: number | null;

}

interface UserContextType {
  user: User | null;
  loading: boolean;
  credits: number | null;
  imageCredits: number | null;
  logout: () => void;
  decrementCredits: () => void;
  decrementImageCredits: () => void;
   
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export const UserProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [credits, setCredits] = useState<number | null>(null);
  const [imageCredits, setImageCredits] = useState<number | null>(null);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      
      if (firebaseUser) {
   
        const userDoc = await getDoc(doc(db, "users", firebaseUser.uid));
  
        // 🔥 Fix: Query the subscribedPlan subcollection
        const subscribedPlanQuery = collection(db, "users", firebaseUser.uid, "subscribedPlan");
        const querySnapshot = await getDocs(subscribedPlanQuery);
  
        let planCredits = null;
        let planImageCredits = null;
        let planName = null;  
        if (!querySnapshot.empty) {
          const subscribedPlanDoc = querySnapshot.docs[0]; // Get first plan document
          const planData = subscribedPlanDoc.data();
          planCredits = planData.planCredits ?? null;
          planImageCredits = planData.planImageCredits ?? null;
          planName = planData.planName ?? null;
        }
  
        const fullname = userDoc.exists() ? userDoc.data().fullname : null;
        setUser({
          uid: firebaseUser.uid,
          email: firebaseUser.email,
          fullname: fullname,
          planName,
          planCredits,
          planImageCredits
          
        });
        setCredits(planCredits);
        setImageCredits(planImageCredits);
      } else {
        setUser(null);
        setCredits(null);
        setImageCredits(null);
      }
      setLoading(false);
    });
  
    return () => unsubscribe();
  }, []);
  
  const logout = async () => {
    await signOut(auth);
    setUser(null);
  };

  const decrementCredits: () => void = () => {
    setCredits((prevCredits: number | null) => {
      if (prevCredits === null) {
        return null;
      }
      return prevCredits - 1;
    });
  };

  const decrementImageCredits: () => void = () => {
    setImageCredits((prevImageCredits: number | null) => {
      if (prevImageCredits === null) {
        return null;
      }
      return prevImageCredits - 1;
    });
  };

  return (
    <UserContext.Provider
      value={{
        user,
        loading,
        credits,
        imageCredits,
        logout,
        decrementCredits,
        decrementImageCredits,
      }}
    >
      {children}
    </UserContext.Provider>
  );
};

// Custom hook to use the user context
export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
};
