import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// Define your site URLs
const baseUrl = 'https://burstmode.ai';
const routes = [
  { url: '/', changefreq: 'weekly', priority: 1.0 },
  { url: '/model', changefreq: 'weekly', priority: 0.9 },
  { url: '/model/headshots', changefreq: 'weekly', priority: 0.8 },
  { url: '/model/foods', changefreq: 'weekly', priority: 0.8 },
  { url: '/model/products', changefreq: 'weekly', priority: 0.8 },
  { url: '/model/virtual-try-on', changefreq: 'weekly', priority: 0.8 },
  { url: '/model/image-to-video', changefreq: 'weekly', priority: 0.8 },
  { url: '/model/interior', changefreq: 'weekly', priority: 0.8 },
  { url: '/model/upscaling', changefreq: 'weekly', priority: 0.8 },
  { url: '/gallery', changefreq: 'weekly', priority: 0.7 },
  { url: '/pricing', changefreq: 'monthly', priority: 0.9 },
  { url: '/about-us', changefreq: 'monthly', priority: 0.6 },
  { url: '/privacy-policy', changefreq: 'yearly', priority: 0.5 },
  { url: '/terms', changefreq: 'yearly', priority: 0.5 },
  { url: '/login', changefreq: 'monthly', priority: 0.7 },
  { url: '/register', changefreq: 'monthly', priority: 0.7 },
  { url: '/suggest-feature', changefreq: 'monthly', priority: 0.6 },
  { url: '/contact-us', changefreq: 'monthly', priority: 0.6 },
];

export async function GET() {
  // Get current date in ISO format
  const today = new Date().toISOString().split('T')[0];

  // Generate XML sitemap
  let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
  xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';

  // Add each route to the sitemap
  routes.forEach(route => {
    xml += '  <url>\n';
    xml += `    <loc>${baseUrl}${route.url}</loc>\n`;
    xml += `    <lastmod>${today}</lastmod>\n`;
    xml += `    <changefreq>${route.changefreq}</changefreq>\n`;
    xml += `    <priority>${route.priority}</priority>\n`;
    xml += '  </url>\n';
  });

  xml += '</urlset>';

  // Write to the public directory
  const publicDir = path.join(process.cwd(), 'public');
  fs.writeFileSync(path.join(publicDir, 'sitemap.xml'), xml);

  return NextResponse.json({ success: true, message: 'Sitemap generated successfully' });
}
