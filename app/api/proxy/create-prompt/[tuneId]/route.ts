import { NextRequest, NextResponse } from "next/server";
import { createAstriaPromptFormData } from "@/lib/astria-form-utils";

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ tuneId: string }> }
) {
  try {
    const { tuneId } = await params;
    const formData = await request.formData();

    const prompt = formData.get("prompt");
    const user = formData.get("user");
    const enhancementType = formData.get("enhancement_type") as string | null;
    // Removed negative prompt as it's not supported on Flux
    const inputImage = formData.get("input_image") as File | null;
    const controlnet = formData.get("controlnet") as string | null;
    const denoisingStrength = formData.get("denoising_strength") as
      | string
      | null;
    const style = formData.get("style") as string | null;
    const garmentModelId = formData.get("garment_model_id") as string | null;

    // Interior design specific parameters
    const controlnets = formData.get("controlnets") as string | null;
    const controlnetWeights = formData.get("controlnet_weights") as
      | string
      | null;
    const maskPrompt = formData.get("mask_prompt") as string | null;
    const maskInvert = formData.get("mask_invert") as string | null;

    // Build the complete prompt string first
    let promptText = "";

    // For virtual try-on, include both person model and garment model in the prompt
    if (garmentModelId) {
      promptText = `person wearing garment <faceid:${garmentModelId}:1.0>, professional fashion photography, studio lighting, ${prompt} --control_guidance_end 0.5`; // --control_guidance_end 0.5 is added to reduce the amount of controlnet guidance. This is to ensure that the model does not overly rely on the controlnet guidance.
    } else {
      promptText = prompt as string;
    }

    if (maskPrompt) {
      // Add mask prompt parameter to the prompt text
      promptText += ` --mask_prompt ${maskPrompt}${
        maskInvert === "true" ? " --mask_invert" : ""
      }`;
    }

    // Handle interior design specific parameters
    if (controlnets) {
      promptText += `--controlnets ${controlnets} --controlnet_weights ${controlnetWeights}`;
    }

    // Create Astria FormData using centralized utility
    const astriaFormData = createAstriaPromptFormData({
      prompt: promptText,
      user: user as string,
      tuneId,
      enhancementType: enhancementType || undefined,
      inputImage,
      controlnet,
      denoisingStrength,
      style,
      garmentModelId,
      controlnets: controlnets ? controlnets.split(' ') : undefined,
      controlnetWeights: controlnetWeights ? controlnetWeights.split(' ') : undefined,
      maskPrompt: maskPrompt || undefined,
      maskInvert: maskInvert || undefined,
      backendVersion: "0",
    });

    // Handle special case for controlnet_txt2img
    if (!inputImage) {
      astriaFormData.append("prompt[controlnet_txt2img]", "true");
    }

    console.log(astriaFormData);


    const url = `${process.env.NEXT_PUBLIC_ASTRIA_BASE_URL}/tunes/${tuneId}/prompts`;
    const res = await fetch(url, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${process.env.NEXT_PUBLIC_ASTRIA_API_KEY}`,
      },
      body: astriaFormData,
    });

    if (!res.ok) {
      const errorData = await res.json();
      console.error("Astria API Error:", errorData);
      return NextResponse.json({ error: res.statusText }, { status: 500 });
    }

    return NextResponse.json(await res.json());
  } catch (error) {
    console.error("Error in POST request:", error);
    return NextResponse.json(
      { error: "An error occurred while processing your request" },
      { status: 500 }
    );
  }
}
