import { NextRequest, NextResponse } from "next/server";
import { createAstriaTuneFormData } from "@/lib/astria-form-utils";

const ASTRIATESTMODE = process.env.ASTRIA_TEST_MODE || "false";
/**
 * Creates a new fine-tune model on Astria AI from training images
 *
 * Required parameters:
 * - name: A class name that describes the fine-tune (e.g., man, woman, cat, dog)
 * - title: Describes the fine-tune, ideally a UUID related to the transaction
 * - images: An array of images to train the fine-tune with
 *
 * Optional parameters:
 * - callback: A webhook URL to be called when the tune is finished training
 * - branch: Enum: sd15, sdxl1, fast, flux1 (default: flux1 or base_tune branch if specified)
 * - steps: Training steps (recommended to leave blank for system defaults)
 * - token: Unique short text to which features will be embedded (default: ohwx for SDXL, sks for SD15)
 * - face_crop: Detects faces in training images and augments training set with cropped faces
 * - training_face_correct: Enhance training images using GFPGAN for low quality images
 * - base_tune_id: Training on top of former fine-tune or different baseline model
 * - model_type: Enum: lora, pti, faceid, null for checkpoint
 * - auto_extend: Boolean to automatically extend tune when it expires
 * - preset: Enum: flux-lora-focus, flux-lora-portrait, flux-lora-fast
 * - characteristics: Object for templatizing prompts text
 * - prompts_attributes: Array of prompts entities
 */
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();

    // Required parameters
    const user = formData.get("user");
    const title = formData.get("title");
    const images = formData.getAll("images");
    const enhancementType = formData.get("enhancementType");

    // Optional parameters
    const callback = formData.get("callback");
    const steps = formData.get("steps");
    const token = formData.get("token") || "ohwx";
    const faceCrop = formData.get("face_crop");
    const trainingFaceCorrect = formData.get("training_face_correct");
    const baseTuneId = formData.get("base_tune_id");
    const modelType = formData.get("model_type") || "lora";
    const autoExtend = formData.get("auto_extend");
    const preset = formData.get("preset") || "flux-lora-portrait";
    const characteristics = formData.get("characteristics");
    const promptsAttributes = formData.get("prompts_attributes");
    const className = formData.get("className") || "";

    // Validate required parameters
    if (!user || !title || !images.length || !enhancementType) {
      return NextResponse.json(
        {
          error:
            "Missing required fields: user, title, enhancementType, and at least one image are required",
        },
        { status: 400 }
      );
    }

    // Validate className for faceid models
    if (modelType === "faceid" && !className) {
      return NextResponse.json(
        {
          error:
            "Missing required field: className is required for faceid models",
        },
        { status: 400 }
      );
    }

    // Determine branch based on test mode and enhancement type
    let branch = "flux1";
    if (ASTRIATESTMODE === "true") {
      branch = "fast";
    } else if (enhancementType === "upscaling") {
      branch = "sd15";
    }

    // Create Astria FormData using centralized utility
    const astriaFormData = createAstriaTuneFormData({
      title: title as string,
      user: modelType === "faceid" ? (className as string) : (user as string),
      images: images as File[],
      modelType: modelType as string,
      enhancementType: enhancementType as string,
      className: modelType === "faceid" ? (className as string) : undefined,
      branch,
      preset: preset as string,
    });

    // Add callback URL to be notified when the tune is finished training
    const callbackUrl = `${
      process.env.NEXT_PUBLIC_APP_URL
    }/api/callback/astria-tune?enhancementType=${encodeURIComponent(
      enhancementType as string
    )}&modelType=${encodeURIComponent(
      modelType as string
    )}&userId=${encodeURIComponent(user as string)}`;
    astriaFormData.append("tune[callback]", callbackUrl);

    // Add token for non-faceid models
    if (modelType !== "faceid") {
      astriaFormData.append("tune[token]", token as string);
    }

    // Add optional parameters if provided
    if (callback) astriaFormData.append("tune[callback]", callback as string);
    if (steps) astriaFormData.append("tune[steps]", steps as string);
    if (faceCrop) astriaFormData.append("tune[face_crop]", faceCrop as string);
    if (trainingFaceCorrect)
      astriaFormData.append(
        "tune[training_face_correct]",
        trainingFaceCorrect as string
      );
    if (baseTuneId)
      astriaFormData.append("tune[base_tune_id]", baseTuneId as string);
    if (autoExtend)
      astriaFormData.append("tune[auto_extend]", autoExtend as string);
    if (characteristics)
      astriaFormData.append("tune[characteristics]", characteristics as string);
    if (promptsAttributes)
      astriaFormData.append(
        "tune[prompts_attributes]",
        promptsAttributes as string
      );

    console.log("astria formdata", astriaFormData);

    // Call the Astria API
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_ASTRIA_BASE_URL}/tunes`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${process.env.NEXT_PUBLIC_ASTRIA_API_KEY}`,
        },
        body: astriaFormData,
      }
    );

    // Handle API response
    if (!response.ok) {
      const errorText = await response.text();
      console.error("Astria API error:", errorText);
      return NextResponse.json(
        { error: errorText },
        { status: response.status }
      );
    }

    // Return successful response
    const tuneDetails = await response.json();
    return NextResponse.json({
      success: true,
      tuneId: tuneDetails.id,
      details: tuneDetails,
    });
  } catch (error) {
    console.error("Error creating tune:", error);
    return NextResponse.json(
      { error: (error as Error).message },
      { status: 500 }
    );
  }
}
