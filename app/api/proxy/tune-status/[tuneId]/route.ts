import { NextRequest, NextResponse } from 'next/server'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ tuneId: string }> }
) {
  const { tuneId } = await params;

  const url = `${process.env.NEXT_PUBLIC_ASTRIA_BASE_URL}/tunes/${tuneId}.json`;
  const res = await fetch(url, {
    headers: { Authorization: `Bearer ${process.env.NEXT_PUBLIC_ASTRIA_API_KEY}` },
  });

  return NextResponse.json(await res.json());
}