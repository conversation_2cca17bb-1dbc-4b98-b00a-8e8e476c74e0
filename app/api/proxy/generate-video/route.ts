import { NextRequest, NextResponse } from "next/server";
import { createAstriaVideoFormData } from "@/lib/astria-form-utils";

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const prompt = formData.get("prompt") as string;
    const user = formData.get("user") as string;
    const tuneId = formData.get("tune_id") as string;
    const inputImage = formData.get("input_image") as File | null;
    const controlnet = formData.get("controlnet") as string | null;
    const denoisingStrength = formData.get("denoising_strength") as string | null;
    const style = formData.get("style") as string | null;
    const videoPrompt = formData.get("video_prompt") as string | null;
    const videoModel = formData.get("video_model") as string | null;

    // Validate required parameters
    if (!user || !prompt || !tuneId) {
      return NextResponse.json(
        { error: "Missing required parameters: user, prompt, and tune_id are required" },
        { status: 400 }
      );
    }

    // Construct the prompt text with video flags
    let promptText = `${prompt} --video`;

    // Add video prompt if provided
    if (videoPrompt && videoPrompt.trim()) {
      promptText += ` --video_prompt "${videoPrompt.trim()}"`;
    }

    // Add video model if provided (720p or 480p)
    if (videoModel && (videoModel === '720p' || videoModel === '480p')) {
      promptText += ` --video_model ${videoModel}`;
    }

    // Create Astria FormData using centralized utility
    const astriaFormData = createAstriaVideoFormData({
      prompt: promptText,
      user,
      tuneId,
      videoPrompt,
      videoModel,
      inputImage,
      controlnet,
      denoisingStrength,
      style,
      backendVersion: "0",
    });

    // Call Astria API
    const url = `${process.env.NEXT_PUBLIC_ASTRIA_BASE_URL}/tunes/${tuneId}/prompts`;
    const res = await fetch(url, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${process.env.NEXT_PUBLIC_ASTRIA_API_KEY}`,
      },
      body: astriaFormData,
    });

    if (!res.ok) {
      const errorData = await res.json();
      console.error("Astria API Error:", errorData);
      return NextResponse.json(
        { error: res.statusText },
        { status: res.status }
      );
    }

    // Return the response from Astria
    return NextResponse.json(await res.json());
  } catch (error) {
    console.error("Error in POST request:", error);
    return NextResponse.json(
      { error: "An error occurred while processing your request" },
      { status: 500 }
    );
  }
}
