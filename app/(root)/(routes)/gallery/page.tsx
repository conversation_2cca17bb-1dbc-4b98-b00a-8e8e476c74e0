"use client";

import { useState } from "react";
import Image from "next/image";

interface GalleryItem {
  id: string;
  category: "headshot" | "food" | "product";
  originalImage: string;
  enhancedImage: string;
}

// Sample data structure - replace with your actual data
const galleryData: GalleryItem[] = [
  // Headshots (images 1-6)
  {
    id: "1",
    category: "headshot",
    originalImage: "/enhancementGallery/1Before.png",
    enhancedImage: "/enhancementGallery/1After.png"
  },
  {
    id: "2",
    category: "headshot",
    originalImage: "/enhancementGallery/2Before.png",
    enhancedImage: "/enhancementGallery/2After.png"
  },
  {
    id: "3",
    category: "headshot",
    originalImage: "/enhancementGallery/3Before.png",
    enhancedImage: "/enhancementGallery/3After.png"
  },
  // Food (images 7-12)
  {
    id: "4",
    category: "food",
    originalImage: "/enhancementGallery/Food1Before.png",
    enhancedImage: "/enhancementGallery/Food1After.png"
  },
  {
    id: "5",
    category: "food",
    originalImage: "/enhancementGallery/Food2Before.png",
    enhancedImage: "/enhancementGallery/Food2After.png"
  },
  {
    id: "6",
    category: "food",
    originalImage: "/enhancementGallery/Food3Before.png",
    enhancedImage: "/enhancementGallery/Food3After.png"
  },
  // Products (images 13-16)
  {
    id: "7",
    category: "product",
    originalImage: "/enhancementGallery/Product1Before.png",
    enhancedImage: "/enhancementGallery/Product1After.png"
  },
  {
    id: "8",
    category: "product",
    originalImage: "/enhancementGallery/Product2Before.png",
    enhancedImage: "/enhancementGallery/Product2After.png"
  }
];

export default function GalleryPage() {
  const [selectedImage, setSelectedImage] = useState<GalleryItem | null>(null);

  return (
    <div className="container mx-auto px-4 py-8 pt-20">
      {!selectedImage ? (
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {galleryData.map((item) => (
            <div
              key={item.id}
              className="group cursor-pointer overflow-hidden rounded-xl shadow-sm hover:shadow-xl transition-all duration-300"
              onClick={() => setSelectedImage(item)}
            >
              <div className="relative aspect-square">
                <Image
                  src={item.enhancedImage}
                  alt={`Enhanced ${item.category}`}
                  fill
                  className="object-cover transform group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-4">
                  <p className="text-white text-sm font-medium capitalize">{item.category}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="fixed inset-0 bg-black/95 z-50 overflow-y-auto">
          <div className="min-h-screen px-4 py-8">
            <div className="max-w-7xl mx-auto space-y-8">
              {/* Close button */}
              <button
                onClick={() => setSelectedImage(null)}
                className="group px-5 py-2.5 bg-zinc-800/50 hover:bg-zinc-700/50 rounded-lg text-sm font-medium transition-all duration-300 flex items-center gap-2 backdrop-blur-sm"
              >
                <span className="group-hover:-translate-x-0.5 transition-transform duration-300">←</span>
                Back to Gallery
              </button>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
                {/* Original Image */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-white/90 relative z-10 bg-zinc-800/50 inline-block px-4 py-1.5 rounded-lg backdrop-blur-sm">
                    Original
                  </h3>
                  <div className="relative aspect-square rounded-2xl overflow-hidden shadow-2xl ring-1 ring-white/10">
                    <Image
                      src={selectedImage.originalImage}
                      alt="Original"
                      fill
                      className="object-cover"
                      priority
                    />
                  </div>
                </div>
                
                {/* Enhanced Image */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-white/90 relative z-10 bg-zinc-800/50 inline-block px-4 py-1.5 rounded-lg backdrop-blur-sm">
                    Enhanced
                  </h3>
                  <div className="relative aspect-square rounded-2xl overflow-hidden shadow-2xl ring-1 ring-white/10">
                    <Image
                      src={selectedImage.enhancedImage}
                      alt="Enhanced"
                      fill
                      className="object-cover"
                      priority
                    />
                  </div>
                </div>
              </div>
              
              <div className="mt-8 text-center">
                <span className="px-6 py-2.5 bg-zinc-800/50 rounded-lg text-base font-medium capitalize text-white/90 backdrop-blur-sm inline-flex items-center gap-2">
                  <span className="w-2 h-2 rounded-full bg-emerald-400/80"></span>
                  {selectedImage.category}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
