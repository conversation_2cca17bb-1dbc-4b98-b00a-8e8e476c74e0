"use client";

import { useState, Suspense } from "react";
import { useUser } from "@/context/UserContext";
import {
  updateUserProfile,
  updateUserPassword,
  deleteUserAccount,
} from "@/lib/firebase";
import { Button } from "@/components/ui/Button";
import { Input } from "@/components/ui/Input";
import { useToast } from "@/hooks/use-toast";
import {
  LogOut,
  Trash,
  Save,
  Edit,
  CreditCard,
  Zap,
  Image,
  User,
  Lock,
  Crown,
  RefreshCw,
} from "lucide-react";
import { usePremiumStatus } from "@/lib/stripe";
import { Drawer } from "vaul";
import { cn } from "@/lib/utils";
import { useEffect } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import AuthLoadingScreen from "@/components/model/AuthLoadingScreen";

// Component that uses useSearchParams - needs to be wrapped in Suspense
const AccountSettingsWithParams = () => {
  const searchParams = useSearchParams();
  const tab = searchParams.get("tab") || "profile";

  return <AccountSettingsContent initialTab={tab} />;
};

// Main account settings component
const AccountSettingsContent = ({ initialTab }: { initialTab: string }) => {
  const { user, logout, loading: userLoading } = useUser();
  const { toast } = useToast();
  const router = useRouter();

  const [fullname, setFullname] = useState(user?.fullname || "");
  const [email, setEmail] = useState(user?.email || "");
  const [password, setPassword] = useState("");
  const [currentPassword, setCurrentPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [isEditingName, setIsEditingName] = useState(false);
  const [isEditingEmail, setIsEditingEmail] = useState(false);
  const premiumStatus = usePremiumStatus(user);
  const [activeTab, setActiveTab] = useState(initialTab);
  const [showPlanModal, setShowPlanModal] = useState(false);

  useEffect(() => {
    setActiveTab(initialTab);
  }, [initialTab]);

  const handleTabClick = (tabId: string) => {
    setActiveTab(tabId);
    router.push(`/account-settings?tab=${tabId}`, { scroll: false });
  };

  useEffect(() => {
    setFullname(user?.fullname || "");
    setEmail(user?.email || "");
  }, [user]);
  const handleUpdateProfile = async () => {
    setLoading(true);
    const response = await updateUserProfile({ fullname, email });
    setLoading(false);

    if (response.success) {
      toast({
        title: "Profile Updated",
        description: "Your changes have been saved.",
      });
      setIsEditingName(false);
      setIsEditingEmail(false);
    } else {
      toast({
        title: "Update Failed",
        description: response.message,
        variant: "destructive",
      });
    }
  };

  useEffect(() => {
    if (!user && !userLoading) {
      router.push("/login");
    }
  }, [user, router, userLoading]);

  if (userLoading) {
    return <AuthLoadingScreen />;
  }

  const handleUpdatePassword = async () => {
    if (!password) return;
    setLoading(true);
    const response = await updateUserPassword(currentPassword, password);
    setLoading(false);

    if (response.success) {
      toast({
        title: "Password Updated",
        description: "Your password has been changed.",
      });
    } else {
      toast({
        title: "Update Failed",
        description: "Incorrect Password Entered",
        variant: "destructive",
      });
    }
  };

  const handleDeleteAccount = async () => {
    if (
      !confirm(
        "Are you sure you want to delete your account? This action cannot be undone."
      )
    )
      return;
    setLoading(true);
    const response = await deleteUserAccount();
    setLoading(false);

    if (response.success) {
      toast({
        title: "Account Deleted",
        description: "Your account has been removed.",
      });
      logout();
    } else {
      toast({
        title: "Deletion Failed",
        description: response.message,
        variant: "destructive",
      });
    }
  };

  const tabs = [
    { id: "profile", label: "Profile", icon: User },
    { id: "password", label: "Password", icon: Lock },
    { id: "subscription", label: "Subscription", icon: Crown },
  ];

  return (
    <div className="min-h-screen bg-black text-white overflow-y-auto">
      <div className="max-w-2xl mx-auto p-2 md:p-8 md:mt-24 mt-5">
        <h1 className="text-4xl font-bold mb-8 bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent">
          Account Settings
        </h1>

        {/* Tabs Navigation */}
        <div className="mb-8">
          <nav className="flex space-x-2 max-w-screen overflow-x-auto">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => handleTabClick(tab.id)}
                className={cn(
                  "flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-200",
                  activeTab === tab.id
                    ? "bg-purple-600 text-white"
                    : "text-gray-400 hover:text-white hover:bg-purple-600/20"
                )}
              >
                <tab.icon className="w-4 h-4" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        <div className="bg-zinc-900 rounded-xl p-6 shadow-xl border border-purple-500/20">
          {activeTab === "profile" && (
            <>
              <div className="space-y-6">
                <div className="bg-zinc-900 rounded-xl p-6 shadow-xl border border-purple-500/20">
                  {activeTab === "profile" && (
                    <>
                      <div className="space-y-6">
                        {/* Name Section */}
                        <div>
                          <div>
                            <label className="text-sm font-medium text-gray-300">
                              Full Name
                            </label>
                            <Button
                              onClick={() => setIsEditingName(true)}
                              variant="ghost"
                              className="hover:bg-purple-600/20  sm:hidden"
                            >
                              <Edit className="mr-2 h-4 w-4" />
                            </Button>
                          </div>
                          {isEditingName ? (
                            <div className="flex items-center flex-col sm:flex-row gap-2 mt-2">
                              <Input
                                value={fullname} // use the state value instead of user.fullname
                                onChange={(e) => setFullname(e.target.value)}
                                placeholder="Enter full name"
                                className="bg-zinc-800 text-white border-zinc-700"
                              />
                              <div className="flex gap-2 items-center flex-1 w-full">
                                <Button
                                  onClick={handleUpdateProfile}
                                  disabled={loading}
                                  className="bg-purple-600 hover:bg-purple-700 w-full flex"
                                >
                                  <Save className="mr-2 h-4 w-4 " />
                                  Save
                                </Button>
                                <Button
                                  onClick={() => setIsEditingName(false)}
                                  variant="outline"
                                  className="border-zinc-700 w-full flex"
                                >
                                  Cancel
                                </Button>
                              </div>
                            </div>
                          ) : (
                            <div className="flex items-center justify-between mt-2">
                              <span className="text-white">
                                {fullname || "No Name"}
                              </span>
                              <Button
                                onClick={() => setIsEditingName(true)}
                                variant="ghost"
                                className="hover:bg-purple-600/20 hidden sm:flex"
                              >
                                <Edit className="mr-2 h-4 w-4 " />
                                Change Name
                              </Button>
                            </div>
                          )}
                        </div>

                        {/* Email Section */}
                        <div>
                          <div>
                            <label className="text-sm font-medium text-gray-300">
                              Email
                            </label>
                            <Button
                              onClick={() => setIsEditingEmail(true)}
                              variant="ghost"
                              className="hover:bg-purple-600/20  sm:hidden"
                            >
                              <Edit className="mr-2 h-4 w-4" />
                            </Button>
                          </div>
                          {isEditingEmail ? (
                            <div className="flex flex-col sm:flex-row items-center gap-2 mt-2">
                              <Input
                                value={email} // use the state variable for email
                                onChange={(e) => setEmail(e.target.value)}
                                placeholder="Enter email"
                                className="bg-zinc-800 border-zinc-700"
                              />
                              <div className="flex gap-2 items-center flex-1 w-full">
                                <Button
                                  onClick={handleUpdateProfile}
                                  disabled={loading}
                                  className="bg-purple-600 hover:bg-purple-700 w-full"
                                >
                                  <Save className="mr-2 h-4 w-4" />
                                  Save
                                </Button>
                                <Button
                                  onClick={() => setIsEditingEmail(false)}
                                  variant="outline"
                                  className="border-zinc-700 w-full"
                                >
                                  Cancel
                                </Button>
                              </div>
                            </div>
                          ) : (
                            <div className="flex items-center justify-between mt-2">
                              <span className="text-white">
                                {email || "No Email"}
                              </span>
                              <Button
                                onClick={() => setIsEditingEmail(true)}
                                variant="ghost"
                                className="hover:bg-purple-600/20 hidden sm:flex"
                              >
                                <Edit className="mr-2 h-4 w-4" />
                                Change Email
                              </Button>
                            </div>
                          )}
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </>
          )}

          {activeTab === "password" && (
            <div className="space-y-4">
              <Input
                type="password"
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
                placeholder="Current password"
                className="bg-zinc-800 border-zinc-700"
              />
              <Input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="New password"
                className="bg-zinc-800 border-zinc-700"
              />
              <Button
                onClick={handleUpdatePassword}
                disabled={loading}
                className="w-full bg-purple-600 hover:bg-purple-700"
              >
                Update Password
              </Button>
            </div>
          )}

          {activeTab === "subscription" && (
            <div className="text-center space-y-4">
              {premiumStatus ? (
                <>
                  <div className="bg-gradient-to-br from-purple-500 to-purple-800 p-6 rounded-lg">
                    <h3 className="text-2xl font-bold mb-2">
                      {user?.planName}
                    </h3>
                    <p className="text-purple-200">Active Plan</p>
                  </div>
                  <div className="flex gap-5 items-center justify-center">
                    <Button
                      variant="outline"
                      onClick={() => setShowPlanModal(true)}
                      className="mt-4 border-purple-500 text-purple-400 hover:bg-purple-600/20"
                    >
                      <Crown className="mr-2 h-4 w-4" />
                      View Plan Details
                    </Button>
                    <Link
                      href="/pricing"
                      className="flex items-center p-1 rounded mt-4 bg-purple-600 hover:bg-purple-700 border-purple-500 text-white hover:bg-purple-600/20"
                    >
                      <RefreshCw className="mr-2 h-4 w-4" />
                      Update Plan
                    </Link>
                  </div>
                </>
              ) : (
                <>
                  <div className="bg-zinc-800 p-6 rounded-lg">
                    <h3 className="text-xl font-semibold mb-2">
                      No Active Plan
                    </h3>
                    <p className="text-gray-400">
                      Upgrade to access premium features
                    </p>
                  </div>
                  <Link href="/pricing">
                    <Button className="mt-4 bg-purple-600 hover:bg-purple-700">
                      <Crown className="mr-2 h-4 w-4" />
                      Upgrade Now
                    </Button>
                  </Link>
                </>
              )}
            </div>
          )}
        </div>

        {/* Danger Zone */}
        <div className="mt-8 border border-red-500/20 rounded-lg p-6 bg-zinc-900">
          <h3 className="text-lg font-semibold text-red-400">Danger Zone</h3>
          <p className="text-sm text-gray-400 mt-1 mb-4">
            Once you delete your account, there is no going back.
          </p>
          <Button
            onClick={handleDeleteAccount}
            variant="destructive"
            className="bg-red-600 hover:bg-red-700"
          >
            <Trash className="mr-2 h-4 w-4" />
            Delete Account
          </Button>
        </div>

        {/* Logout Button */}
        <div className="mt-6">
          <Button
            onClick={logout}
            variant="ghost"
            className="text-gray-400 hover:text-white hover:bg-purple-600/20"
          >
            <LogOut className="mr-2 h-4 w-4" />
            Log Out
          </Button>
        </div>
      </div>

      {/* Plan Details Drawer */}
      <Drawer.Root open={showPlanModal} onOpenChange={setShowPlanModal}>
        <Drawer.Portal>
          <Drawer.Overlay className="fixed inset-0 bg-black/60" />
          <Drawer.Content className="bg-black flex flex-col rounded-t-[10px] mt-24 fixed bottom-0 left-0 right-0 border-t border-purple-500/20">
            <div className="p-6">
              <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-zinc-800 mb-8" />
              <div className="max-w-md mx-auto">
                <Drawer.Title className="font-bold mb-6 text-2xl bg-gradient-to-r from-purple-400 to-purple-600 bg-clip-text text-transparent">
                  Premium Plan Details
                </Drawer.Title>

                {/* Plan Status */}
                <div className="bg-gradient-to-br from-purple-500 to-purple-800 text-white rounded-lg p-6 mb-6">
                  <h3 className="text-2xl font-bold mb-2">{user?.planName}</h3>
                  <p className="text-purple-200">Active Subscription</p>
                </div>

                {/* Credits Section */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-zinc-900 rounded-lg border border-purple-500/20">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-purple-600/20 rounded-full">
                        <Zap className="w-5 h-5 text-purple-400" />
                      </div>
                      <div>
                        <p className="font-medium text-white">Models</p>
                        <p className="text-sm text-gray-400">
                          Remaining models
                        </p>
                      </div>
                    </div>
                    <span className="text-xl font-bold text-purple-400">
                      {user?.planCredits}
                    </span>
                  </div>

                  <div className="flex items-center justify-between p-4 bg-zinc-900 rounded-lg border border-purple-500/20">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-purple-600/20 rounded-full">
                        <Image className="w-5 h-5 text-purple-400" />
                      </div>
                      <div>
                        <p className="font-medium text-white">Image Credits</p>
                        <p className="text-sm text-gray-400">
                          For image generation
                        </p>
                      </div>
                    </div>
                    <span className="text-xl font-bold text-purple-400">
                      {user?.planImageCredits}
                    </span>
                  </div>
                </div>

                <div className="mt-6">
                  <Button
                    onClick={() => setShowPlanModal(false)}
                    variant="ghost"
                    className="w-full text-gray-400 hover:text-white hover:bg-purple-600/20"
                  >
                    Close
                  </Button>
                </div>
              </div>
            </div>
          </Drawer.Content>
        </Drawer.Portal>
      </Drawer.Root>
    </div>
  );
};

// Main component with Suspense wrapper
const AccountSettings = () => {
  return (
    <Suspense fallback={<AuthLoadingScreen />}>
      <AccountSettingsWithParams />
    </Suspense>
  );
};

export default AccountSettings;
