import React from "react";
import UpscalingWrapper from "@/components/model/UpscalingWrapper";
import { Metadata } from 'next';
import Script from 'next/script';

export const metadata: Metadata = {
  title: "AI Image Upscaling & Resolution Enhancement | Burst Mode",
  description: "Enhance your image quality with Burst Mode's AI upscaling technology. Increase resolution, improve details, and create print-ready images from low-quality photos.",
  keywords: [
    "AI image upscaling",
    "photo resolution enhancement",
    "image quality improvement",
    "super-resolution AI",
    "high-resolution conversion",
    "image detail enhancement",
    "AI photo enlargement",
    "print-quality image conversion",
    "low-resolution image fix",
    "image clarity enhancement"
  ],
  openGraph: {
    title: "AI Image Upscaling & Resolution Enhancement | Burst Mode",
    description: "Enhance your image quality with Burst Mode's AI upscaling technology. Increase resolution, improve details, and create print-ready images from low-quality photos.",
    url: "https://burstmode.ai/model/upscaling",
    siteName: "Burst Mode AI",
    images: [
      {
        url: "https://burstmode.ai/og-upscaling.JPG",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Image Upscaling",
      }
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "AI Image Upscaling & Resolution Enhancement | Burst Mode",
    description: "Enhance your image quality with Burst Mode's AI upscaling technology. Increase resolution and improve details.",
    images: [
      {
        url: "https://burstmode.ai/og-upscaling.JPG",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Image Upscaling",
      }
    ],
  },
  alternates: {
    canonical: "https://burstmode.ai/model/upscaling",
  },
};

const UpscalingPage = () => {
  return (
    <>
      {/* Structured Data for SEO */}
      <Script
        id="upscaling-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": "AI Image Upscaling & Resolution Enhancement",
            "description": "Enhance your image quality with Burst Mode's AI upscaling technology. Increase resolution, improve details, and create print-ready images from low-quality photos.",
            "url": "https://burstmode.ai/model/upscaling",
            "mainEntity": {
              "@type": "Service",
              "name": "AI Image Upscaling & Resolution Enhancement",
              "provider": {
                "@type": "Organization",
                "name": "Burst Mode AI",
                "url": "https://burstmode.ai"
              },
              "description": "Our AI-powered image upscaling service transforms low-resolution images into high-quality, detailed visuals. Perfect for print materials, digital displays, and professional presentations.",
              "offers": {
                "@type": "Offer",
                "price": "15.00",
                "priceCurrency": "USD"
              },
              "serviceType": "AI Image Enhancement"
            }
          })
        }}
      />
      <UpscalingWrapper enhancementType="upscaling" />
    </>
  );
};

export default UpscalingPage;
