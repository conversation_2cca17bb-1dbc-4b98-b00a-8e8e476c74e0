import React from "react";
import AvatarWrapper from "@/components/model/AvatarWrapper";
import { Metadata } from 'next';
import Script from 'next/script';

export const metadata: Metadata = {
  title: "AI Avatar Generation | Burst Mode",
  description: "Create stunning AI-generated avatars with Burst Mode's technology. Perfect for social media profiles, gaming, and personal branding.",
  keywords: [
    "AI avatar generation",
    "profile picture creation",
    "digital avatar",
    "personalized avatar",
    "AI profile pictures",
    "custom avatar creation",
    "social media avatars",
    "gaming avatars",
    "virtual identity",
    "personal branding avatars"
  ],
  openGraph: {
    title: "AI Avatar Generation | Burst Mode",
    description: "Create stunning AI-generated avatars with Burst Mode's technology. Perfect for social media profiles, gaming, and personal branding.",
    url: "https://burstmode.ai/model/avatar",
    siteName: "Burst Mode AI",
    images: [
      {
        url: "https://burstmode.ai/og-avatar.png",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Avatar Generation",
      }
    ],
    type: "website",
  },
};

const AvatarGenerationPage = () => {
  return (
    <>
      {/* Structured Data for SEO */}
      <Script
        id="avatar-generation-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": "AI Avatar Generation",
            "description": "Create stunning AI-generated avatars with Burst Mode's technology. Perfect for social media profiles, gaming, and personal branding.",
            "url": "https://burstmode.ai/model/avatar",
            "mainEntity": {
              "@type": "Service",
              "name": "AI Avatar Generation",
              "provider": {
                "@type": "Organization",
                "name": "Burst Mode AI",
                "url": "https://burstmode.ai"
              },
              "description": "Our AI-powered avatar generation service creates personalized, high-quality digital avatars. Perfect for social media profiles, gaming avatars, and personal branding.",
              "offers": {
                "@type": "Offer",
                "price": "15.00",
                "priceCurrency": "USD"
              },
              "serviceType": "AI Avatar Generation"
            }
          })
        }}
      />
      <AvatarWrapper />
    </>
  );
};

export default AvatarGenerationPage;
