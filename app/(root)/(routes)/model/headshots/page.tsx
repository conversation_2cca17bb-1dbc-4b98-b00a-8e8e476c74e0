import React from "react";
import EnhancementWrapper from "@/components/model/EnhancementWrapper";
import { Metadata } from 'next';
import Script from 'next/script';

export const metadata: Metadata = {
  title: "Professional AI Headshots | Burst Mode",
  description: "Transform your selfies into professional headshots with Burst Mode's AI technology. Perfect for LinkedIn profiles, business cards, resumes, and professional portfolios.",
  keywords: [
    "AI headshots",
    "professional profile pictures",
    "LinkedIn headshots",
    "business portrait AI",
    "professional photo enhancement",
    "corporate headshots",
    "resume photo enhancement",
    "professional selfie transformation",
    "business profile pictures",
    "AI portrait enhancement"
  ],
  openGraph: {
    title: "Professional AI Headshots | Burst Mode",
    description: "Transform your selfies into professional headshots with Burst Mode's AI technology. Perfect for LinkedIn profiles, business cards, resumes, and professional portfolios.",
    url: "https://burstmode.ai/model/headshots",
    siteName: "Burst Mode AI",
    images: [
      {
        url: "https://burstmode.ai/og-headshot.jpg",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Professional Headshots",
      }
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Professional AI Headshots | Burst Mode",
    description: "Transform your selfies into professional headshots with Burst Mode's AI technology.",
    images: [
      {
        url: "https://burstmode.ai/app/og-headshot.jpg",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Professional Headshots",
      }
    ],
  },
  alternates: {
    canonical: "https://burstmode.ai/model/headshots",
  },
};

const HeadshotEnhancementPage = () => {
  return (
    <>
      {/* Structured Data for SEO */}
      <Script
        id="headshot-enhancement-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": "Professional AI Headshots",
            "description": "Transform your selfies into professional headshots with Burst Mode's AI technology. Perfect for LinkedIn profiles, business cards, resumes, and professional portfolios.",
            "url": "https://burstmode.ai/model/headshots",
            "mainEntity": {
              "@type": "Service",
              "name": "AI Headshot Enhancement",
              "provider": {
                "@type": "Organization",
                "name": "Burst Mode AI",
                "url": "https://burstmode.ai"
              },
              "description": "Our AI-powered headshot enhancement service transforms ordinary selfies into polished, professional portraits. Perfect for LinkedIn profiles, business cards, resumes, and professional portfolios.",
              "offers": {
                "@type": "Offer",
                "price": "15.00",
                "priceCurrency": "USD"
              },
              "serviceType": "AI Photography Enhancement"
            }
          })
        }}
      />
      <EnhancementWrapper enhancementType="headshot" />
    </>
  );
};

export default HeadshotEnhancementPage;