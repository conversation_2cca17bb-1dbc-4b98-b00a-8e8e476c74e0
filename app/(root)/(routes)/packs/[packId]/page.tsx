import AstriaPacksGrid from "@/components/photo-packs/astria-packs-grid";
import { AstriaPacksService } from "@/lib/astria-packs";

interface PacksPageProps {
  params: Promise<{ packId: string }>;
}

export default async function PacksPage({ params }: PacksPageProps) {
  const { packId } = await params;
  const astriaPacksService = new AstriaPacksService();
  const pack = await astriaPacksService.getPackById(Number(packId));

  return <AstriaPacksGrid pack={pack.data} />;
}
