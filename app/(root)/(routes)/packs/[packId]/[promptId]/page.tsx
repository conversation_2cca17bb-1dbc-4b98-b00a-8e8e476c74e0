import { PromptData } from "@/types/astria";
import { AstriaPromptService } from "@/lib/astria-prompts";
import AstriaPacksPage from "@/components/photo-packs/astria-pack-page";

interface TuneInfo {
  id: number;
  title: string;
}

export interface AstriaPromptData {
  id: number;
  text: string;
  images: string[];
  tune?: TuneInfo;
  tunes?: TuneInfo[];
  created_at: string;
  url: string;
}

interface PackPageProps {
  params: Promise<{
    packId: string;
    promptId: string;
  }>;
}

export default async function PacksPage({ params }: PackPageProps) {
  const { packId, promptId } = await params;

  let promptData: AstriaPromptData | null = null;
  let fetchError: string | null = null;

  try {
    const astriaPromptService = new AstriaPromptService();
    // For pack prompts, we fetch by prompt ID only since packs have their own prompt structure
    const promptApiResponse = await astriaPromptService.getPromptById(promptId);

    if (promptApiResponse.success && promptApiResponse.data) {
      promptData = promptApiResponse.data;
    } else {
      fetchError = promptApiResponse.message || "Prompt data not found or API response is not in the expected format.";
      console.warn("API Response:", promptApiResponse);
    }
  } catch (e) {
    console.error(
      `Failed to fetch prompt data for packId: ${packId}, promptId: ${promptId}:`,
      e
    );
    fetchError = "Failed to load prompt information. Please try again later.";
  }

  if (fetchError || !promptData) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center text-white p-4">
        <p>{fetchError || "Prompt data could not be loaded."}</p>
      </div>
    );
  }
  return <AstriaPacksPage initialPromptData={promptData as PromptData} />;
}
