// import { AstriaGenerateWithPackPromt } from "@/components/photo-packs/astria-generate-with-pack-prompt";
// import { AstriaPromptService } from "@/lib/astria-prompts";
// import { AstriaPromptResponse, PromptData } from "@/types/astria";

// interface GenerateWithPackPromptProps {
//   params: {
//     packId: string;
//     promptId: string;
//   };
// }

// export default async function GenerateWithPackPrompt({
//   params,
// }: GenerateWithPackPromptProps) {
//   const { packId, promptId } = await params;
//   const astriaPromptService = new AstriaPromptService();
//   const promptApiResponse =
//     (await astriaPromptService.getPromptBytuneIdAndPromptId(
//       packId,
//       promptId
//     )) as AstriaPromptResponse;

//   return (
//     <AstriaGenerateWithPackPromt
//       promtData={promptApiResponse.data as PromptData}
//     />
//   );
// }

import { AstriaGenerateWithPackPromt } from "@/components/photo-packs/astria-generate-with-pack-prompt";
import { AstriaPromptService } from "@/lib/astria-prompts";
import { AstriaPromptResponse, PromptData } from "@/types/astria";
import { notFound } from "next/navigation"; // Import for Next.js notFound helper

interface GenerateWithPackPromptProps {
  params: {
    packId: string;
    promptId: string;
  };
}

export default async function GenerateWithPackPrompt({
  params,
}: GenerateWithPackPromptProps) {
  const { packId, promptId } = params; // params is already destructured, no need for await

  const astriaPromptService = new AstriaPromptService();

  const promptApiResponse: AstriaPromptResponse =
    await astriaPromptService.getPromptBytuneIdAndPromptId(packId, promptId);

  // --- Start of Error Handling ---
  if (!promptApiResponse.success) {
    // Log the error on the server side for debugging
    console.error(
      `Failed to fetch prompt for packId: ${packId}, promptId: ${promptId}. Error: ${promptApiResponse.message}`
    );

    // Option 1: Display a user-friendly error message directly
    // This is good for showing specific context to the user.
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh] text-center">
        <h1 className="text-3xl font-bold text-red-600 mb-4">
          Error Loading Prompt
        </h1>
        <p className="text-lg text-gray-700 mb-2">
          {promptApiResponse.message || "An unexpected error occurred."}
        </p>
        <p className="text-md text-gray-500">
          The requested prompt could not be found or there was an issue fetching
          it. Please check the URLs or try again later.
        </p>
        {/* You can add a link back to a main page or a relevant section */}
        <a
          href="/dashboard"
          className="mt-6 px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Go to Dashboard
        </a>
      </div>
    );

    // Option 2: Use Next.js notFound() helper
    // This will render the closest `not-found.tsx` file in your project.
    // It's a good generic way to handle 404s.
    // notFound();
  }
  // --- End of Error Handling ---

  // If we reach here, it means promptApiResponse.success is true
  // and promptApiResponse.data will contain the prompt data.
  return (
    <AstriaGenerateWithPackPromt
      promtData={promptApiResponse.data as PromptData}
    />
  );
}
