import AboutUs from '@/components/aboutUs/AboutUs';
import { Metadata } from 'next';
import Script from 'next/script';

export const metadata: Metadata = {
  title: "About Burst Mode - Our Mission & Team | AI Photography Platform",
  description: "Learn about Burst Mode's mission to empower individuals and businesses with AI-powered photography tools. Meet our team of AI engineers, UX designers, and photography experts.",
  keywords: [
    "Burst Mode team",
    "AI photography company",
    "about Burst Mode",
    "AI image generation team",
    "professional headshots AI company",
    "Burst Mode mission",
    "AI photography experts",
    "photo enhancement company"
  ],
  openGraph: {
    title: "About Burst Mode - Our Mission & Team | AI Photography Platform",
    description: "Learn about Burst Mode's mission to empower individuals and businesses with AI-powered photography tools. Meet our team of AI engineers, UX designers, and photography experts.",
    url: "https://burstmode.ai/about-us",
    siteName: "Burst Mode AI",
    images: [
      {
        url: "https://burstmode.ai/app/og-icon.png",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Photography Team",
      }
    ],
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "About Burst Mode - Our Mission & Team | AI Photography Platform",
    description: "Learn about Burst Mode's mission to empower individuals and businesses with AI-powered photography tools.",
    images: [
      {
        url: "https://burstmode.ai/app/og-icon.png",
        width: 1200,
        height: 630,
        alt: "Burst Mode AI Photography Team",
      }
    ],
  },
  alternates: {
    canonical: "https://burstmode.ai/about-us",
  },
};

const AboutUsPage = () => {
  return (
    <>
      {/* Structured Data for SEO */}
      <Script
        id="about-page-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "AboutPage",
            "name": "About Burst Mode",
            "description": "Learn about Burst Mode's mission to empower individuals and businesses with AI-powered photography tools.",
            "url": "https://burstmode.ai/about-us",
            "mainEntity": {
              "@type": "Organization",
              "name": "Burst Mode AI",
              "url": "https://burstmode.ai",
              "logo": "https://burstmode.ai/app/icon.png",
              "description": "AI-powered photography enhancement and generation platform",
              "foundingDate": "2023",
              "knowsAbout": [
                "AI Photography",
                "Image Generation",
                "Professional Headshots",
                "Food Photography",
                "Product Photography"
              ]
            }
          })
        }}
      />
      <AboutUs />
    </>
  );
}

export default AboutUsPage;