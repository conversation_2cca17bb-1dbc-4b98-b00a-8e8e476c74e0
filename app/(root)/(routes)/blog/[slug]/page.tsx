import MagazineBlogPostVariant from "@/components/blog/MagazineBlogPostVariant";
import { Metadata } from "next";
import { blogPosts } from "@/types/data";
import Script from 'next/script';
import { articleSchema } from "@/lib/structuredData";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug: string }>;
}): Promise<Metadata> {
  const { slug } = await params;
  const post = blogPosts.find((post) => post.slug === slug);

  if (!post) {
    return {
      title: "Blog Post Not Found",
      description: "The requested blog post could not be found.",
    };
  }

  // Generate keywords based on post category and title
  const keywords = [
    post.category,
    ...post.title.split(' ').filter(word => word.length > 3),
    "Burst Mode",
    "AI photography",
    "photography tips",
  ];

  return {
    title: `${post.title} | Burst Mode Blog`,
    description: post.excerpt,
    keywords: keywords,
    openGraph: {
      title: post.title,
      description: post.excerpt,
      type: "article",
      publishedTime: post.date,
      images: [
        {
          url: post.coverImage || "/placeholder.svg",
          width: 1200,
          height: 630,
          alt: post.title,
        },
      ],
      url: `https://burstmode.ai/blog/${slug}`,
      siteName: "Burst Mode AI",
    },
    twitter: {
      card: "summary_large_image",
      title: post.title,
      description: post.excerpt,
      images: [
        {
          url: post.coverImage || "/placeholder.svg",
          width: 1200,
          height: 630,
          alt: post.title,
        }
      ],
      creator: "@burstmodeai",
      site: "@burstmodeai",
    },
    alternates: {
      canonical: `https://burstmode.ai/blog/${slug}`,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    authors: [{ name: "Burst Mode AI Team" }],
    category: post.category,
  };
}

export default async function BlogPost({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params;
  const post = blogPosts.find((post) => post.slug === slug);

  return (
    <>
      {post && (
        <Script
          id="blog-post-schema"
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(
              articleSchema(
                post.title,
                post.excerpt,
                post.coverImage || "/placeholder.svg",
                post.date,
                post.date,
                "Burst Mode AI Team"
              )
            ),
          }}
        />
      )}
      <MagazineBlogPostVariant slug={slug} />
    </>
  );
}
