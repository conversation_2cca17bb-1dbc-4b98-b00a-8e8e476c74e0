import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image';
import { cn } from '@/lib/utils';

interface ModelTrainingLoadingScreenProps {
  modelType?: 'lora' | 'faceid';
  enhancementType?: string;
}

const ModelTrainingLoadingScreen: React.FC<ModelTrainingLoadingScreenProps> = ({ 
  modelType = 'lora',
  enhancementType = 'virtual try-on'
}) => {
  const [progress, setProgress] = useState(0);
  const [phase, setPhase] = useState(1);
  const [factIndex, setFactIndex] = useState(0);
  
  const interestingFacts = [
    "Model training can take 10-15 minutes to complete",
    "Your model will be available for 30 days after creation",
    "Higher quality input images result in better models",
    "The AI analyzes thousands of features in your images",
    "Virtual try-on requires both person and garment models",
    "The callback API will save your model automatically when training completes",
  ];

  // Cycle through facts during long waits
  useEffect(() => {
    if (phase >= 2) {
      const factInterval = setInterval(() => {
        setFactIndex(prev => (prev + 1) % interestingFacts.length);
      }, 5000);
      return () => clearInterval(factInterval);
    }
  }, [phase, interestingFacts.length]);
  
  // Simulate progress with smoother asymptotic behavior
  useEffect(() => {
    const interval = setInterval(() => {
      setProgress(prev => {
        // Phase 1: Normal progress up to 30% with smoother increments
        if (phase === 1 && prev < 30) {
          const increment = Math.max(0.1, Math.random() * 0.5);
          const newValue = prev + increment;
          
          if (newValue >= 30) {
            setPhase(2);
            return 30;
          }
          return newValue;
        }
        // Phase 2: Slower progress from 30% to 60% with smoother transition
        else if (phase === 2 && prev < 60) {
          const increment = Math.max(0.05, Math.random() * 0.2);
          const newValue = prev + increment;
          
          if (newValue >= 60) {
            setPhase(3);
            return 60;
          }
          return newValue;
        }
        // Phase 3: Very slow progress approaching but never reaching 100%
        else if (phase === 3) {
          const increment = Math.max(0.01, Math.random() * 0.05) * (95 - prev) / 10;
          return Math.min(95, prev + increment);
        }
        
        return prev;
      });
    }, 200); // More frequent updates for smoother animation
    
    return () => clearInterval(interval);
  }, [phase]);

  // Get a loading message based on the current phase and model type
  const getLoadingMessage = () => {
    const modelTypeText = modelType === 'lora' ? 'person' : 'garment';
    
    if (phase === 1) return `Preparing ${modelTypeText} model training...`;
    if (phase === 2) return `Training ${modelTypeText} model...`;
    return `Finalizing ${modelTypeText} model...`;
  };

  return (
    <motion.div 
      className="flex flex-col items-center justify-center w-full h-full min-h-[600px] bg-gradient-to-br from-indigo-900/50 to-purple-900/50 backdrop-blur-sm rounded-xl p-8"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <div className="relative mb-12">
        {/* Burst animation effect */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={`burst-${i}`}
            className="absolute w-3 h-12 bg-gradient-to-t from-pink-500 to-blue-500 rounded-full"
            style={{ 
              originX: 0.5,
              originY: 0,
              left: '50%',
              top: '50%',
              marginLeft: -6,
              marginTop: -6,
            }}
            animate={{
              rotate: i * 45,
              scaleY: [1, 1.2, 1],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              repeatType: 'reverse',
              delay: i * 0.1,
            }}
          />
        ))}
        
        {/* Central orb with properly masked icon */}
        <motion.div 
          className="relative z-10 w-20 h-20 bg-gradient-to-br from-indigo-900 to-purple-900 rounded-full flex items-center justify-center shadow-lg shadow-purple-500/50 overflow-hidden"
          animate={{ 
            scale: [1, 1.1, 1],
            boxShadow: [
              '0 0 20px 5px rgba(168, 85, 247, 0.4)',
              '0 0 30px 8px rgba(168, 85, 247, 0.6)',
              '0 0 20px 5px rgba(168, 85, 247, 0.4)'
            ]
          }}
          transition={{ 
            duration: 2,
            repeat: Infinity,
            repeatType: 'reverse'
          }}
        >
          {/* Circular mask for the image */}
          <div className="w-12 h-12 rounded-full overflow-hidden flex items-center justify-center">
            <Image 
              src="/app/icon-original.png" 
              alt="BurstMode AI" 
              width={48} 
              height={48} 
              className="object-cover"
              style={{ borderRadius: '50%' }}
            />
          </div>
        </motion.div>
      </div>
      
      {/* Enhanced progress visualization */}
      <div className="mb-8 relative">
        {/* Progress bar with smoother animation */}
        <div className="w-64 h-3 bg-gray-700/60 backdrop-blur-sm rounded-full overflow-hidden mb-4 shadow-inner">
          <motion.div 
            className="h-full bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 relative"
            style={{ width: `${Math.min(progress, 100)}%` }}
            transition={{ duration: 0.1 }}
          >
            {/* Shimmering effect on the progress bar */}
            <motion.div 
              className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white to-transparent opacity-30"
              animate={{ x: ['-100%', '100%'] }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            />
          </motion.div>
        </div>
        
        {/* Enhanced percentage indicator */}
        <motion.div 
          className="absolute -right-10 -top-1 px-2 py-1 bg-white/20 backdrop-blur-sm rounded-md text-xs font-medium text-white"
          animate={{ 
            scale: progress > 90 ? [1, 1.05, 1] : 1,
          }}
          transition={{ 
            duration: 0.8, 
            repeat: progress > 90 ? Infinity : 0,
            repeatType: 'reverse'
          }}
        >
          {Math.round(progress)}%
        </motion.div>
      </div>
      
      {/* Primary message */}
      <div className="text-center mb-6">
        <motion.p 
          className={cn(
            "text-xl md:text-2xl font-bold bg-gradient-to-r from-purple-400 via-blue-500 to-purple-400 bg-clip-text text-transparent animate-[gradient-slide_5s_linear_infinite] bg-[length:200%_200%]",
          )}
          animate={{ opacity: [0.7, 1, 0.7] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          {getLoadingMessage()}
        </motion.p>
        <p className="text-sm text-white/80 mt-2">
          This process may take 10-15 minutes to complete
        </p>
      </div>
      
      {/* Model type indicator */}
      <div className="mb-6 flex items-center justify-center">
        <div className={`px-3 py-1 rounded-full ${modelType === 'lora' ? 'bg-blue-500/20 text-blue-300' : 'bg-purple-500/20 text-purple-300'} text-sm font-medium`}>
          {modelType === 'lora' ? 'Person Model (lora)' : 'Garment Model (faceid)'}
        </div>
      </div>
      
      {/* Interesting facts section with better transitions */}
      <motion.div 
        className="max-w-md bg-white/10 backdrop-blur-sm rounded-lg p-4 mt-2 border border-white/20"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <AnimatePresence mode="wait">
          <motion.p 
            key={factIndex}
            className="text-sm text-center text-white"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.5 }}
          >
            <span className="text-pink-300 font-medium">Note: </span>
            {interestingFacts[factIndex]}
          </motion.p>
        </AnimatePresence>
      </motion.div>
      
      {/* Callback information */}
      <motion.div 
        className="mt-6 text-xs text-white/60 text-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 2, duration: 1 }}
      >
        Your model will be automatically saved when training completes
      </motion.div>
    </motion.div>
  );
};

export default ModelTrainingLoadingScreen;
