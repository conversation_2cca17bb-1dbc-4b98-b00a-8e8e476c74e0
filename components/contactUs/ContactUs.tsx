'use client';

import React from 'react';
import { Button } from '@/components/ui/Button';

const ContactUs = () => {
  return (
    <div className="w-full flex items-center flex-col justify-center px-4 py-16 lg:px-8 lg:py-24 gap-12">
      <div className="text-center space-y-4 max-w-3xl">
        <h1 className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
          Contact Us
        </h1>
        <p className="text-lg text-muted-foreground">
          Have questions or need assistance? We're here to help!
        </p>
        <p className="text-lg text-muted-foreground">
          Contact us via email: <a href="mailto:<EMAIL>" className="text-primary hover:underline"><EMAIL></a>
        </p>
      </div>

      <div className="w-full max-w-3xl bg-secondary/5 backdrop-blur-sm rounded-2xl p-8 border border-primary/10">
        <form className="flex flex-col gap-6">
          <div className="flex flex-col gap-2">
            <label htmlFor="name" className="text-sm font-medium text-muted-foreground">
              Name
            </label>
            <input
              type="text"
              id="name"
              className="w-full px-4 py-2 rounded-xl bg-secondary/10 border border-primary/20 focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Your name"
            />
          </div>

          <div className="flex flex-col gap-2">
            <label htmlFor="email" className="text-sm font-medium text-muted-foreground">
              Email
            </label>
            <input
              type="email"
              id="email"
              className="w-full px-4 py-2 rounded-xl bg-secondary/10 border border-primary/20 focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Your email"
            />
          </div>

          <div className="flex flex-col gap-2">
            <label htmlFor="subject" className="text-sm font-medium text-muted-foreground">
              Subject
            </label>
            <select
              id="subject"
              className="w-full px-4 py-2 rounded-xl bg-secondary/10 border border-primary/20 focus:outline-none focus:ring-2 focus:ring-primary"
            >
              <option value="general">General Inquiry</option>
              <option value="billing">Billing Issue</option>
              <option value="technical">Technical Support</option>
              <option value="other">Other</option>
            </select>
          </div>

          <div className="flex flex-col gap-2">
            <label htmlFor="message" className="text-sm font-medium text-muted-foreground">
              Message
            </label>
            <textarea
              id="message"
              rows={6}
              className="w-full px-4 py-2 rounded-xl bg-secondary/10 border border-primary/20 focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Your message here..."
            />
          </div>

          <Button
            type="submit"
            className="bg-primary/80 hover:bg-primary/50 text-secondary py-3 px-6 rounded-xl text-lg font-medium transition-all duration-300"
          >
            Submit
          </Button>
        </form>
      </div>
    </div>
  );
};

export default ContactUs;