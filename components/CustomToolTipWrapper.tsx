import {
  Tooltip,
  Toolt<PERSON>Content,
  Too<PERSON>ipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export const CustomToolTipWrapper = ({
  children,
  content,
}: {
  children: React.ReactNode;
  content: string;
}) => {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>{children}</TooltipTrigger>
        <TooltipContent className="bg-amber-950/90 rounded-md border-amber-700/50">
          <p className="text-xs">{content}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
