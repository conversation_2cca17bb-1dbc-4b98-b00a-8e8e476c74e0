import React from "react";

const HeadShot = () => {
  return (
    <div className="flex items-center flex-col justify-center container mx-auto px-4 py-8 lg:px-8 lg:py-16">
      <p className="font-bold text-4xl mb-16 text-center">
        Upload your selfies and generate AI headshots now
      </p>
      <div className="flex max-md:flex-col items-center w-full">
        <div className="grid grid-cols-2 gap-4 items-center justify-center">
          <img src="/headshot_input_1.avif" className="rounded-xl border" />
          <img src="/headshot_input_2.avif" className="rounded-xl border" />
          <img src="/headshot_input_3.avif" className="rounded-xl border" />
          <img src="/headshot_input_4.avif" className="rounded-xl border" />
        </div>
        <img src="/pencil-arrow.avif" alt="" className="invert w-[150px] rotate-12 md:-rotate-45"/>
        <div className="">
          <img src="/headshot_result.jpg" className="rounded-xl border" />
        </div>
      </div>
    </div>
  );
};

export default HeadShot;
