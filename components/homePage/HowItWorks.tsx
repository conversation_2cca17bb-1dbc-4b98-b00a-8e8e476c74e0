"use client";

import React, { useState } from "react";
import { Upload, Wand2, Download } from "lucide-react";
import Image from "next/image";

// 1) Import from your animated-modal
import {
  Modal,
  ModalBody,
  ModalContent,
  useModal,
} from "../ui/animated-modal";

const steps = [
  {
    icon: <Upload className="h-10 w-10 text-gray-800" />,
    title: "Upload",
    description: "Simply upload your photos or select a model",
    image: "/upload-preview.png",
  },
  {
    icon: <Wand2 className="h-10 w-10 text-gray-800" />,
    title: "Generate",
    description: "Our AI will automatically analyze and generate your images",
    image: "/generate-preview.png",
  },
  {
    icon: <Download className="h-10 w-10 text-gray-800" />,
    title: "Download",
    description: "Download your stunning, AI-powered photos in high resolution",
    image: "/download-preview.png",
  },
];

const HowItWorks = () => {
  // 2) State for tracking which image is clicked
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  // We can access setOpen anywhere in this component
  const { setOpen } = useModal();

  // Handler for when a step image is clicked
  const handleImageClick = (imageUrl: string) => {
    setSelectedImage(imageUrl);
    setOpen(true);
  };

  return (
    <div id="how-it-works" className="w-full flex flex-col items-center justify-center px-6 py-16 bg-gray-50 dark:bg-neutral-900">
      {/*
        3) The Modal is placed at the same level as your main container.
        4) Inside it, we conditionally show selectedImage at a larger size.
      */}
      <Modal>
        <ModalBody>
          <ModalContent>
            {selectedImage && (
              <Image
                src={selectedImage}
                alt="Enlarged preview"
                width={2000}
                height={2000}
                className="object-cover rounded-lg"
              />
            )}
            {/* {selectedImage && (
              <p className="mt-4 text-gray-600 dark:text-gray-300 text-center">
                Here's your larger preview
              </p>
            )} */}
          </ModalContent>
        </ModalBody>
      </Modal>

      {/* Rest of your HowItWorks content */}
      <div className="w-full max-w-7xl mx-auto">
        <h2 className="text-4xl md:text-5xl font-bold text-center mb-16">
          How It Works
        </h2>

        {/* Responsive Grid Layout */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {steps.map((step, index) => (
            <div
              key={index}
              className="flex flex-col items-center p-6 bg-white dark:bg-neutral-800 rounded-2xl shadow-lg border border-gray-200 dark:border-neutral-700 transition-transform hover:scale-105"
            >
              <div className="p-4 bg-purple-100 dark:bg-gray-300 rounded-full">
                {step.icon}
              </div>
              <h3 className="text-xl font-semibold mt-4">{step.title}</h3>
              <p className="text-center text-gray-600 dark:text-gray-300 mt-2">
                {step.description}
              </p>

              {/* 5) Clicking this image triggers the modal */}
              <div
                className="w-full mt-6 overflow-hidden rounded-lg shadow-md cursor-pointer"
                onClick={() => handleImageClick(step.image)}
              >
                <Image
                  src={step.image}
                  alt={step.title}
                  width={400}
                  height={300}
                  className="w-full h-auto object-cover"
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default HowItWorks;
