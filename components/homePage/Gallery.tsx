import Image from "next/image";
import React from "react";

const Gallery = () => {
  const numbers = Array.from({ length: 16 }, (_, index) => index + 1);

  // Shuffle the numbers array
  const shuffledNumbers = numbers.sort(() => Math.random() - 0.5);

  // Divide the shuffled numbers into four columns
  const columns: number[][] = [[], [], [], []];
  shuffledNumbers.forEach((num, index) => {
    columns[index % 4].push(num);
  });
  return (
    <div className="w-full flex items-center flex-col justify-center px-4 py-8 lg:px-8 lg:py-16">
      <div className="w-full max-w-7xl mx-auto grid grid-cols-2 lg:grid-cols-4 gap-4">
        {columns.map((column, colIndex) => (
          <div
            key={colIndex}
            className={`flex flex-col gap-4 ${
              colIndex === 0 ? 'pt-8' : colIndex === 2 ? 'pt-12' : colIndex === 3 ? 'pt-24' : ''
            }`}
          >
            {column.map((num) => (
              <Image
                key={num}
                src={`/_gallery/${num}.jpg`}
                alt={`Gallery Image ${num}`}
                width={500}
                height={500}
                className="w-full h-auto rounded-xl border border-white"
              />
            ))}
          </div>
        ))}
      </div>
    </div>
  );
};

export default Gallery;
