const ImageContent = ({
  reversed,
  image,
}: {
  reversed: boolean;
  image: string;
}) => {
  return (
    <div className={`flex ${reversed ? "justify-start" : "justify-end"}`}>
      <div className="relative w-4/5 max-h-[500px] overflow-hidden rounded-2xl border-2 border-primary/20 shadow-2xl">
        <img
          src={image}
          alt=""
          className="w-full h-full object-cover object-top rounded-2xl"
        />
      </div>
    </div>
  );
};

export default ImageContent;
