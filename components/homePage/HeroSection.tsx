'use client';

import React, { useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/Button";
import { Check, ArrowRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

const HeroSection = () => {
  const videoRef = useRef<HTMLVideoElement | null>(null);

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.playbackRate = 3;
    }
  }, []);

  return (
    <div className="w-full relative overflow-hidden min-h-screen">
      <video
        autoPlay
        loop
        muted
        ref={videoRef}
        className="absolute inset-0 w-full h-full object-cover"
      >
        <source src="/herosectionbackground.mp4" type="video/mp4" />
        Your browser does not support the video tag.
      </video>

      {/* Background gradient */}
      {/* <div className="absolute inset-0 bg-gradient-to-b from-primary/10 via-secondary/50 to-primary/90 blur-3xl" /> */}

      <div className="w-full max-w-7xl mx-auto px-4 py-8 lg:px-8 lg:py-16 relative min-h-screen flex items-center">
        <div className="flex flex-col lg:flex-row items-center gap-12 w-full">
          {/* Left content */}
          <div className="lg:w-1/2 flex flex-col gap-8 pt-8">
            <div className="space-y-4">
              <div className="inline-flex items-center gap-2 bg-secondary/50 px-4 py-2 rounded-full">
                <span className="px-2 py-1 bg-primary/10 text-white text-xs rounded-full">
                  New
                </span>
                <span className="text-sm text-primary font-medium">
                  Effortless AI Photo Enhancements
                </span>
              </div>

              <div className="space-y-8 p-4 w-full mt-5 bg-secondary/50 rounded-lg backdrop-blur-sm">
                <h1 className="text-4xl lg:text-6xl font-bold text-primary tracking-tight">
                  Transform Your Photos with {" "}
                  <span className="text-gradient bg-clip-text text-transparent bg-gradient-to-r from-primary to-purple-600">
                    AI!
                  </span>
                </h1>
                <p className="text-lg leading-relaxed">
                  Effortlessly generate enhanced headshots, product images, and food photos 
                  with our cutting-edge AI technology.
                </p>

                <div className="flex flex-col sm:flex-row gap-4">
                  <Link href="/register">
                    <Button
                      variant="default"
                      size="lg"
                      className="w-full sm:w-auto text-lg font-semibold group"
                    >
                      Start Generating Now
                      <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition" />
                    </Button>
                  </Link>
                  <Link href="/gallery">
                    <Button
                      variant="outline"
                      size="lg"
                      className="w-full sm:w-auto text-lg font-semibold"
                    >
                      View Gallery
                    </Button>
                  </Link>
                </div>

              {/* Features list */}
              <div className="grid grid-cols-2 gap-4 mt-5">
                {[
                  "Effortless Enhancement through AI",
                  "Stunning Results",
                  "Fast and Efficient",
                  "User-Friendly Interface",
                  // "Batch Processing Available",Z
                ].map((feature) => (
                  <div key={feature} className="flex items-center gap-2">
                    <div className="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center">
                      <Check className="h-4 w-4 text-primary" />
                    </div>
                    <span className="text-sm font-medium">{feature}</span>
                  </div>
                ))}
              </div>

              {/* Social proof */}
              <div className="flex flex-col sm:flex-row items-center gap-6 mt-5 p-4 bg-secondary/50 rounded-lg backdrop-blur-sm">
                <div className="flex -space-x-3">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <div
                      key={i}
                      className="h-10 w-10 rounded-full border-2 border-white overflow-hidden"
                    >
                      <Image
                        src="/hero-user.png"
                        alt="User avatar"
                        width={40}
                        height={40}
                        className="h-full w-full object-cover"
                      />
                    </div>
                  ))}
                </div>
                <div className="flex flex-col">
                  <p className="font-semibold">123,456,789+</p>
                  <p className="text-sm text-muted-foreground">
                    AI generated images
                  </p>
                </div>
              </div>
              </div>
            </div>
          </div>

          {/* Right content - Preview Image */}
          <div className="lg:w-1/2 relative mt-20">
            <div className="relative aspect-square max-w-[500px] mx-auto">
              {/* Add your hero image here */}
              <div className="absolute inset-0 bg-gradient-to-tr from-primary to-purple-600 rounded-3xl blur-3xl opacity-20" />
              <div className="relative bg-secondary/50 backdrop-blur-sm rounded-3xl p-2 border border-primary/10">
                {/* Replace with your actual preview image */}
                <div className="aspect-square rounded-2xl bg-secondary overflow-hidden">
                  <Image
                    src="/_gallery/16.jpg"
                    alt="AI Generation Preview"
                    width={500}
                    height={500}
                    className="object-cover"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
