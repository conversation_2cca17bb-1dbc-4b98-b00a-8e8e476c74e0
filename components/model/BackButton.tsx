"use client";

import React from "react";
import { motion } from "framer-motion";

interface BackButtonProps {
  onClick: () => void;
  label?: string;
}

const BackButton: React.FC<BackButtonProps> = ({ 
  onClick, 
  label = "Back to model selection" 
}) => {
  return (
    <motion.button
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="mt-4 text-white/70 hover:text-white flex items-center"
      onClick={onClick}
    >
      ← {label}
    </motion.button>
  );
};

export default BackButton;
