"use client";

import Image from "next/image";
import Link from "next/link";
import React from "react";
import { CardContainer, CardBody, CardItem } from "../ui/3d-card";
import { modelCards } from "@/lib/constants";

const Model = () => {
  return (
    <div className="pt-20">
      <div className="container mx-auto max-w-screen-2xl grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5 p-2  sm:p-10">
        {modelCards.map((card, index) => (
          <CardContainer key={index} className="inter-var w-full h-full" containerClassName="p-0">
            <CardBody className="bg-gray-50  relative group/card dark:hover:shadow-2xl dark:hover:shadow-emerald-500/[0.1] dark:bg-black dark:border-white/[0.2] border-black/[0.1] w-full h-[400px] rounded-xl p-6 border flex flex-col justify-between">
              <div className="flex flex-col h-full">
                <CardItem
                  translateZ={50}
                  className="text-xl font-bold text-neutral-600 dark:text-white"
                >
                  {card.title}
                </CardItem>
                <CardItem translateZ={100} className="w-full mt-4 flex-grow">
                  <div className="w-full h-[240px] relative">
                    {card.imageSrc.endsWith('.mp4') ? (
                      <video
                        src={card.imageSrc}
                        autoPlay
                        loop
                        muted
                        className="rounded-xl w-full h-full object-cover absolute inset-0"
                      />
                    ) : (
                      <Image
                        src={card.imageSrc}
                        alt={card.imageAlt}
                        fill
                        className="rounded-xl object-cover"
                      />
                    )}
                  </div>
                </CardItem>
              </div>
              <CardItem
                translateZ={20}
                as={Link}
                href={card.href}
                className="mt-4 px-4 py-2 rounded-xl bg-black dark:bg-white dark:text-black text-white text-xs font-bold self-start"
              >
                Try now →
              </CardItem>
            </CardBody>
          </CardContainer>
        ))}
      </div>
    </div>
  );
};

export default Model;
