"use client";

import React, { useState } from "react";
import { useAuthState } from "react-firebase-hooks/auth";
import { auth } from "@/lib/firebase";
import { ModelSelector } from "@/components/modelTraining";
import ImageToVideoPrompt from "@/components/model/ImageToVideoPrompt";
import { motion, AnimatePresence } from "framer-motion";
import BurstModeLoadingScreen from "../ui/GenerationLoadingScreen";
import { Video } from "lucide-react";
import { EnhancementType } from "@/types/componentsProps";
import AuthLoadingScreen from "./AuthLoadingScreen";
import AuthRequiredScreen from "./AuthRequiredScreen";
import PremiumRequiredScreen from "./PremiumRequiredScreen";
import { usePremiumStatus } from "@/lib/stripe";

interface ImageToVideoWrapperProps {
  enhancementType: string;
}

const ImageToVideoWrapper: React.FC<ImageToVideoWrapperProps> = ({
  enhancementType,
}) => {
  const [user, loadingUser] = useAuthState(auth);
  const premiumStatus = usePremiumStatus(user);
  // const premiumStatus = true;

  const [selectedModelId, setSelectedModelId] = useState<string>("");
  const [selectedModelTitle, setSelectedModelTitle] = useState<string>("");
  const [isTrainingStarted, setIsTrainingStarted] = useState(false);
  const [isTrainingFailed, setIsTrainingFailed] = useState(false);
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [videoUrl, setVideoUrl] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  // Handle model selection
  const handleModelSelected = (modelId: string, modelTitle: string) => {
    setSelectedModelId(modelId);
    setSelectedModelTitle(modelTitle);
  };

  // Handle training start
  const handleTrainingStarted = () => {
    setIsTrainingStarted(true);
  };

  // Handle training failure
  const handleTrainingFailed = () => {
    setIsTrainingFailed(true);
    setIsTrainingStarted(false);
  };

  // Handle processing start
  const handleStartProcessing = () => {
    setIsProcessing(true);
  };

  // Handle processing completion
  const handleCompleteProcessing = () => {
    setIsProcessing(false);
  };

  if (loadingUser) {
    return <AuthLoadingScreen />;
  }

  if (!user) {
    return <AuthRequiredScreen enhancementType={enhancementType} />;
  }

  if (!premiumStatus) {
    return <PremiumRequiredScreen enhancementType={enhancementType} />;
  }

  return (
    <div className="container mx-auto px-2 sm:px-4 py-8 pt-20">
      <div className="max-w-5xl mx-auto">
        <div className="mb-8 text-center">
          <h1 className="text-3xl font-bold text-white mb-2 flex flex-col md:flex-row items-center justify-center gap-2">
            <Video className="h-14 w-14 md:h-10 md:w-10 text-purple-400" />
            Image to Video Generation
          </h1>
          <p className="text-gray-400 max-w-2xl mx-auto">
            Create stunning videos from images. First, train a model with your
            photos, then generate videos with custom prompts.
          </p>
        </div>

        {!selectedModelId ? (
          <div className="bg-gradient-to-b from-black/20 to-black/5 border rounded-lg p-2 sm:p-6 ">
            <ModelSelector
              user={user}
              enhancementType={enhancementType as EnhancementType}
              onModelSelected={handleModelSelected}
              onTrainingStarted={handleTrainingStarted}
              onTrainingFailed={handleTrainingFailed}
            />
          </div>
        ) : (
          <AnimatePresence mode="wait">
            {isProcessing ? (
              <motion.div
                key="processing"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
              >
                <BurstModeLoadingScreen message="Generating Video. \n This may take a few minutes..." />
              </motion.div>
            ) : (
              <motion.div
                key="prompt"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ duration: 0.3 }}
              >
                <ImageToVideoPrompt
                  modelId={selectedModelId}
                  userId={user.uid}
                  title={selectedModelTitle}
                  imageUrl={imageUrl}
                  setImageUrl={setImageUrl}
                  videoUrl={videoUrl}
                  setVideoUrl={setVideoUrl}
                  onStartProcessing={handleStartProcessing}
                  onCompleteProcessing={handleCompleteProcessing}
                  enhancementType={enhancementType}
                />
              </motion.div>
            )}
          </AnimatePresence>
        )}
      </div>
    </div>
  );
};

export default ImageToVideoWrapper;
