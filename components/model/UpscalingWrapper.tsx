"use client";

import React, { useState } from "react";
import { useAuthState } from "react-firebase-hooks/auth";
import { auth } from "@/lib/firebase";
import { motion, AnimatePresence } from "framer-motion";
import BurstModeLoadingScreen from "../ui/GenerationLoadingScreen";

// Imported components
import AuthLoadingScreen from "./AuthLoadingScreen";
import AuthRequiredScreen from "./AuthRequiredScreen";
import PremiumRequiredScreen from "./PremiumRequiredScreen";
import EnhancementHeader from "./EnhancementHeader";
import ScreenContentContainer from "./ScreenContentContainer";
import BackButton from "./BackButton";
import ScreenTransition from "./ScreenTransition";
import { EnhancementType } from "@/types/componentsProps";
import UpscalingPrompt from "./UpscalingPrompt";
import { usePremiumStatus } from "@/lib/stripe";
import { User } from "firebase/auth";

type ScreenState = "upload" | "processing";

interface UpscalingWrapperProps {
  enhancementType: EnhancementType;
}

const UpscalingWrapper: React.FC<UpscalingWrapperProps> = ({
  enhancementType,
}) => {
  const [user, loadingUser] = useAuthState(auth);
  const [screen, setScreen] = useState<ScreenState>("upload");
  const [imageUrl, setImageUrl] = useState<string | null>(null);

  const premiumStatus = usePremiumStatus(user as User | null | undefined);
  // const premiumStatus = true;

  if (loadingUser) {
    return <AuthLoadingScreen />;
  }

  if (!user) {
    return <AuthRequiredScreen enhancementType={enhancementType} />;
  }

  if (!premiumStatus) {
    return <PremiumRequiredScreen enhancementType={enhancementType} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-black/20 to-black/5">
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-4xl mx-auto px-2 sm:px-4 pt-16 md:pt-28 pb-16 w-full"
      >
        <EnhancementHeader
          enhancementType={enhancementType}
          currentScreen={screen === "upload" ? "modelSelection" : "processing"}
        />

        <ScreenContentContainer>
          <div className="p-1 sm:p-5">
            <AnimatePresence mode="wait">
              {screen === "upload" && (
                <ScreenTransition screenKey="upload" direction="left">
                  <UpscalingPrompt
                    userId={user.uid}
                    imageUrl={imageUrl}
                    setImageUrl={setImageUrl}
                    onStartProcessing={() => setScreen("processing")}
                    onCompleteProcessing={() => setScreen("upload")}
                    user={user}
                  />
                </ScreenTransition>
              )}

              {screen === "processing" && <BurstModeLoadingScreen />}
            </AnimatePresence>
          </div>
        </ScreenContentContainer>
      </motion.div>
    </div>
  );
};

export default UpscalingWrapper;
