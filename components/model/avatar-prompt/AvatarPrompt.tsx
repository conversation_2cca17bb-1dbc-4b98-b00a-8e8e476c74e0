"use client";

import React, { useState } from "react";
import { Spark<PERSON> } from "lucide-react";
import { useUser } from "@/context/UserContext";
import { useToast } from "@/hooks/use-toast";
import { collection, doc, getDocs, updateDoc } from "@firebase/firestore";
import { db } from "@/lib/firebase";
import { generateAvatar } from "@/lib/avatar-api";
import ImageUploader from "../image-prompt/ImageUploader";
import StyleSelector from "../image-prompt/StyleSelector";
import { StyleType } from "../image-prompt/types";
import { PREDEFINED_PROMPTS } from "@/lib/constants";
import ImageDisplay from "../image-prompt/ImageDisplay";

interface AvatarPromptProps {
  userId: string;
  imageUrl: string | null;
  setImageUrl: React.Dispatch<React.SetStateAction<string | null>>;
  onStartProcessing: () => void;
  onCompleteProcessing: () => void;
}

export default function AvatarPrompt({
  userId,
  imageUrl,
  setImageUrl,
  onStartProcessing,
  onCompleteProcessing,
}: AvatarPromptProps) {
  const [prompt, setPrompt] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [inputImage, setInputImage] = useState<File | null>(null);
  const [inputImageUrl, setInputImageUrl] = useState<string | null>(null);
  const [controlnetType, setControlnetType] = useState<string | null>("composition");
  const [denoisingStrength, setDenoisingStrength] = useState<number>(0.7);
  const [style, setStyle] = useState<StyleType>(null);
  const [selectedPresetIndex, setSelectedPresetIndex] = useState<number | null>(null);
  
  const { imageCredits, decrementImageCredits } = useUser();
  const { toast } = useToast();

  // Handle preset prompt selection
  const handlePresetSelect = (index: number) => {
    if (PREDEFINED_PROMPTS.avatar && PREDEFINED_PROMPTS.avatar[index]) {
      setPrompt(PREDEFINED_PROMPTS.avatar[index]);
      setSelectedPresetIndex(index);
    }
  };

  const handleSubmit = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    setIsLoading(true);
    onStartProcessing();

    const subscribedPlanRef = collection(db, "users", userId, "subscribedPlan");
    const querySnapshot = await getDocs(subscribedPlanRef);

    if (querySnapshot.empty) {
      setIsLoading(false);
      onCompleteProcessing();
      toast({
        title: "⚠️ Subscription Issue",
        description: "No active subscription plan found. Please check your account settings.",
        variant: "destructive",
        duration: 5000,
      });
      return;
    }

    // Get the first document ID in `subscribedPlan`
    const planDoc = querySnapshot.docs[0];
    const planDocId = planDoc.id;
    const imageCredits = planDoc.data().planImageCredits;

    // Reference the document and decrement imageCredits
    const planRef = doc(db, "users", userId, "subscribedPlan", planDocId);
    await updateDoc(planRef, {
      planImageCredits: imageCredits - 1,
    });

    // Update the local state
    decrementImageCredits();

    try {
      // Prepare form data for image upload if we have an input image
      let formData = null;
      if (inputImage) {
        formData = new FormData();
        formData.append('image', inputImage);
      }

      // Generate avatar
      const result = await generateAvatar({
        user: userId,
        prompt: prompt,
        inputImage: formData,
      });

      if (!result.success) {
        toast({
          title: "⚠️ Avatar Generation Failed",
          description: result.message ?
            (typeof result.message === 'string' ? result.message : JSON.parse(result.message).error) :
            "There was an error generating your avatar. Please try again.",
          variant: "destructive",
          duration: 5000,
        });
        return;
      }

      if (result.data?.length) {
        const redirectUrl = result.data[0];
        setImageUrl(redirectUrl);
      }
    } catch (error) {
      toast({
        title: "⚠️ Process Failed",
        description:
          "There was an error processing your request. Please try again later.",
        variant: "destructive",
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
      onCompleteProcessing();
    }
  };

  return (
    <div className="p-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Left Column - Controls */}
        <div className="space-y-6">
          {/* Preset Prompts */}
          <div className="space-y-2">
            <label className="text-white/90 text-sm font-medium">
              Preset Avatar Styles
            </label>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              {PREDEFINED_PROMPTS.avatar?.map((preset, index) => (
                <button
                  key={index}
                  onClick={() => handlePresetSelect(index)}
                  className={`
                    text-left p-3 rounded-lg text-sm transition-all
                    ${
                      selectedPresetIndex === index
                        ? "bg-purple-600 text-white"
                        : "bg-white/10 hover:bg-white/20 text-white/80"
                    }
                  `}
                >
                  {preset}
                </button>
              ))}
            </div>
          </div>

          {/* Custom Prompt Input */}
          <div className="space-y-2">
            <label className="text-white/90 text-sm font-medium">
              Custom Prompt
            </label>
            <textarea
              value={prompt}
              onChange={(e) => {
                setPrompt(e.target.value);
                setSelectedPresetIndex(null);
              }}
              placeholder="Describe your avatar in detail..."
              className="w-full h-24 bg-white/10 text-white placeholder-white/40 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-purple-500/70"
            />
          </div>

          {/* Image Uploader */}
          <ImageUploader
            inputImage={inputImage}
            setInputImage={setInputImage}
            inputImageUrl={inputImageUrl}
            setInputImageUrl={setInputImageUrl}
            controlnetType={controlnetType}
            setControlnetType={setControlnetType}
            denoisingStrength={denoisingStrength}
            setDenoisingStrength={setDenoisingStrength}
            style={style}
            setStyle={setStyle}
          />
          {/* Generate Button */}
          <div className="pt-2">
            <button
              onClick={(e) => {
                if (imageCredits === 0) {
                  e.preventDefault();
                  toast({
                    title: "⚠️ Out of Image Credits!",
                    description:
                      "You have 0 image credits left. Upgrade your plan to generate more images.",
                    duration: 4000,
                  });
                  return;
                }
                handleSubmit(e);
              }}
              disabled={isLoading || !prompt.trim()}
              title={inputImage ? "Using reference image" : "Text to image generation"}
              className={`
                w-full
                ${
                  prompt.trim()
                    ? "bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-500 hover:to-indigo-500"
                    : "bg-gradient-to-r from-purple-600/40 to-indigo-600/40 cursor-not-allowed"
                }
                text-white
                py-3
                px-6
                rounded-lg
                transition-all
                duration-300
                ease-in-out
                flex items-center justify-center gap-2
                shadow-lg shadow-purple-900/20
              `}
            >
              {isLoading ? (
                <>
                  <div className="h-5 w-5 animate-spin rounded-full border-b-2 border-white"></div>
                  <span>Processing...</span>
                </>
              ) : (
                <>
                  <Sparkles size={18} />
                  <span>
                    {inputImage ? "Generate Avatar from Reference" : "Generate Avatar"}
                  </span>
                </>
              )}
            </button>
            <div className="text-white/60 text-xs mt-2 text-center">
              {imageCredits} image {imageCredits === 1 ? "credit" : "credits"}{" "}
              remaining
            </div>
          </div>
        </div>

        {/* Right Column - Preview */}
        <ImageDisplay
          isLoading={isLoading}
          imageUrl={imageUrl}
          enhancementType="avatar"
          useEnhancedPrompt={false}
          enhancedPrompt={null}
        />
      </div>
    </div>
  );
}
