"use client";

import React, { useState } from "react";
import { useAuthState } from "react-firebase-hooks/auth";
import { auth } from "@/lib/firebase";
import { ModelSelector } from "@/components/modelTraining";
import ImagePrompt from "@/components/model/ImagePrompt";
import { motion, AnimatePresence } from "framer-motion";
import BurstModeLoadingScreen from "../ui/GenerationLoadingScreen";
import { Shirt, User } from "lucide-react";

// Imported extracted components
import AuthLoadingScreen from "./AuthLoadingScreen";
import AuthRequiredScreen from "./AuthRequiredScreen";
import PremiumRequiredScreen from "./PremiumRequiredScreen";
import EnhancementHeader from "./EnhancementHeader";
import ScreenContentContainer from "./ScreenContentContainer";
import BackButton from "./BackButton";
import ScreenTransition from "./ScreenTransition";
import { EnhancementType } from "@/types/componentsProps";
import { usePremiumStatus } from "@/lib/stripe";

type ScreenState = "personModelSelection" | "garmentModelSelection" | "prompt" | "processing";

interface VirtualTryOnWrapperProps {
  enhancementType: EnhancementType;
}

const VirtualTryOnWrapper: React.FC<VirtualTryOnWrapperProps> = ({
  enhancementType,
}) => {
  const [user, loadingUser] = useAuthState(auth);
  const [screen, setScreen] = useState<ScreenState>("personModelSelection");

  // Person model state
  const [personModel, setPersonModel] = useState<string | null>(null);
  const [personModelTitle, setPersonModelTitle] = useState<string | null>(null);

  // Garment model state
  const [garmentModel, setGarmentModel] = useState<string | null>(null);
  const [garmentModelTitle, setGarmentModelTitle] = useState<string | null>(null);

  const [imageUrl, setImageUrl] = useState<string | null>(null);

  const premiumStatus = usePremiumStatus(user);
  // const premiumStatus = true;

  const handlePersonModelSelected = (modelId: string, modelTitle: string) => {
    setPersonModel(modelId);
    setPersonModelTitle(modelTitle);
    setScreen("garmentModelSelection");
  };

  const handleGarmentModelSelected = (modelId: string, modelTitle: string) => {
    setGarmentModel(modelId);
    setGarmentModelTitle(modelTitle);
    setScreen("prompt");
  };


  if (loadingUser) {
    return <AuthLoadingScreen />;
  }

  if (!user) {
    return <AuthRequiredScreen enhancementType={enhancementType} />;
  }

  if (!premiumStatus) {
    return <PremiumRequiredScreen enhancementType={enhancementType} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-black/20 to-black/5">
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-4xl mx-auto px-2 sm:px-4 pt-16 md:pt-28 pb-16 w-full"
      >
        <EnhancementHeader
          enhancementType={enhancementType}
          currentScreen={screen === "personModelSelection" || screen === "garmentModelSelection" ? "modelSelection" : screen}
        />

        <ScreenContentContainer>
          <AnimatePresence mode="wait">
            {screen === "personModelSelection" && (
              <ScreenTransition screenKey="person-model-selection" direction="left">
                <div className="w-full flex flex-col items-center justify-center px-4 py-8">
                  <div className="flex items-center justify-center mb-6 bg-purple-500/20 p-4 rounded-full">
                    <User size={32} className="text-purple-400" />
                  </div>
                  <h2 className="text-xl font-bold mb-8 text-center">Select or Create a Person Model</h2>
                  <p className="text-white/70 text-center mb-8 max-w-md">
                    First, select or create a model of the person who will be wearing the garments.
                    Upload at least 4 high-quality images of the person from different angles.
                    <span className="block mt-2 text-purple-300 font-medium">Note: Only person models (type: lora) will be shown here.</span>
                  </p>
                  <ModelSelector
                    user={user}
                    enhancementType={enhancementType}
                    onModelSelected={handlePersonModelSelected}
                    onTrainingStarted={() => setScreen("processing")}
                    onTrainingFailed={() => setScreen("personModelSelection")}
                    modelType="lora"
                  />
                </div>
              </ScreenTransition>
            )}

            {screen === "garmentModelSelection" && (
              <ScreenTransition screenKey="garment-model-selection" direction="left">
                <div className="w-full flex flex-col items-center justify-center px-4 py-8">
                  <div className="flex items-center justify-center mb-6 bg-purple-500/20 p-4 rounded-full">
                    <Shirt size={32} className="text-purple-400" />
                  </div>
                  <h2 className="text-xl font-bold mb-8 text-center">Select or Create a Garment Model</h2>
                  <p className="text-white/70 text-center mb-8 max-w-md">
                    Now, select or create a model of the garment. Upload at least 4 high-quality images
                    of the garment from different angles. For best results, use images with a clean background.
                    <span className="block mt-2 text-purple-300 font-medium">Note: Only garment models (type: faceid) will be shown here.</span>
                  </p>
                  <ModelSelector
                    user={user}
                    enhancementType={enhancementType}
                    onModelSelected={handleGarmentModelSelected}
                    onTrainingStarted={() => setScreen("processing")}
                    onTrainingFailed={() => setScreen("garmentModelSelection")}
                    modelType="faceid"
                  />
                </div>
              </ScreenTransition>
            )}

            {screen === "prompt" && personModel && personModelTitle && garmentModel && garmentModelTitle && (
              <ScreenTransition screenKey="prompt" direction="right">
                <ImagePrompt
                  userId={user.uid}
                  title={`${personModelTitle} wearing ${garmentModelTitle}`}
                  modelId={personModel}
                  imageUrl={imageUrl}
                  setImageUrl={setImageUrl}
                  onStartProcessing={() => setScreen("processing")}
                  onCompleteProcessing={() => setScreen("prompt")}
                  enhancementType={enhancementType}
                  garmentModelId={garmentModel}
                />
              </ScreenTransition>
            )}

            {screen === "processing" && (
              <BurstModeLoadingScreen
                message="Training your model..."
                showSkipButton={true}
                skipButtonText="Continue while training in background"
                onSkip={() => {
                  // If we were in person model selection, go to garment model selection
                  if (!personModel) {
                    setScreen("personModelSelection");
                  }
                  // If we were in garment model selection, go back to person model selection
                  else if (!garmentModel) {
                    setScreen("garmentModelSelection");
                  }
                  // If both models are selected, go to prompt screen
                  else {
                    setScreen("prompt");
                  }
                }}
              />
            )}
          </AnimatePresence>
        </ScreenContentContainer>

        {screen === "garmentModelSelection" && (
          <BackButton onClick={() => setScreen("personModelSelection")} />
        )}

        {screen === "prompt" && (
          <BackButton onClick={() => setScreen("garmentModelSelection")} />
        )}
      </motion.div>
    </div>
  );
};

export default VirtualTryOnWrapper;
