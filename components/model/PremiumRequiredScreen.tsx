"use client";

import React from "react";
import { motion } from "framer-motion";
import { CreditCard } from "lucide-react";

interface PremiumRequiredScreenProps {
  enhancementType: string;
}

const PremiumRequiredScreen: React.FC<PremiumRequiredScreenProps> = ({ enhancementType }) => {
  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="flex flex-col items-center justify-center h-screen bg-gradient-to-b from-black/40 to-black/10"
    >
      <motion.div 
        className="bg-black/30 p-8 rounded-xl backdrop-blur-md border border-white/10 flex flex-col items-center max-w-sm text-center"
        whileHover={{ scale: 1.02 }}
        transition={{ type: "spring", stiffness: 400, damping: 10 }}
      >
        <CreditCard className="h-10 w-10 text-primary mb-4" />
        <h2 className="text-xl font-bold mb-2 text-white">Premium Access Required</h2>
        <p className="text-white/70 mb-4">A subscription is required to use the {enhancementType} enhancement features.</p>
        <motion.button 
          onClick={() => window.location.href = "/pricing"}
          className="px-6 py-3 bg-primary rounded-lg font-medium text-gray-800"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.98 }}
        >
          View Plans
        </motion.button>
      </motion.div>
    </motion.div>
  );
};

export default PremiumRequiredScreen;
