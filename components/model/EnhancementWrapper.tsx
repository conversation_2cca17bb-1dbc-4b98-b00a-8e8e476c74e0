"use client";

import React, { useState } from "react";
import { useAuthState } from "react-firebase-hooks/auth";
import { auth } from "@/lib/firebase";
import { ModelSelector } from "@/components/modelTraining";
import ImagePrompt from "@/components/model/ImagePrompt";
import { usePremiumStatus } from "@/lib/stripe";
import { motion, AnimatePresence } from "framer-motion";
import BurstModeLoadingScreen from "../ui/GenerationLoadingScreen";

// Imported extracted components
import AuthLoadingScreen from "./AuthLoadingScreen";
import AuthRequiredScreen from "./AuthRequiredScreen";
import PremiumRequiredScreen from "./PremiumRequiredScreen";
import EnhancementHeader from "./EnhancementHeader";
import ScreenContentContainer from "./ScreenContentContainer";
import BackButton from "./BackButton";
import ScreenTransition from "./ScreenTransition";
import { EnhancementType } from "@/types/componentsProps";

type ScreenState = "modelSelection" | "prompt" | "processing";

interface EnhancementWrapperProps {
  enhancementType: EnhancementType;
}

const EnhancementWrapper: React.FC<EnhancementWrapperProps> = ({
  enhancementType,
}) => {
  const [user, loadingUser] = useAuthState(auth);
  const [screen, setScreen] = useState<ScreenState>("modelSelection");
  const [selectedModel, setSelectedModel] = useState<string | null>(null);
  const [selectedTitle, setSelectedTitle] = useState<string | null>(null);
  const [imageUrl, setImageUrl] = useState<string | null>(null);

  const premiumStatus = usePremiumStatus(user);

  const handleModelSelected = (modelId: string, modelTitle: string) => {
    setSelectedModel(modelId);
    setSelectedTitle(modelTitle);
    setScreen("prompt");
  };

  if (loadingUser) {
    return <AuthLoadingScreen />;
  }

  if (!user) {
    return <AuthRequiredScreen enhancementType={enhancementType} />;
  }

  if (!premiumStatus) {
    return <PremiumRequiredScreen enhancementType={enhancementType} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-black/20 to-black/5">
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-4xl mx-auto px-2 sm:px-4 pt-16 md:pt-28 pb-16 w-full"
      >
        <EnhancementHeader
          enhancementType={enhancementType}
          currentScreen={screen}
        />

        <ScreenContentContainer>
          <AnimatePresence mode="wait">
            {screen === "modelSelection" && (
              <ScreenTransition screenKey="model-selection" direction="left">
                <ModelSelector
                  user={user}
                  enhancementType={enhancementType}
                  onModelSelected={handleModelSelected}
                  onTrainingStarted={() => setScreen("processing")}
                  onTrainingFailed={() => setScreen("modelSelection")}
                />
              </ScreenTransition>
            )}

            {screen === "prompt" && selectedModel && selectedTitle && (
              <ScreenTransition screenKey="prompt" direction="right">
                <ImagePrompt
                  userId={user.uid}
                  title={selectedTitle}
                  modelId={selectedModel}
                  imageUrl={imageUrl}
                  setImageUrl={setImageUrl}
                  onStartProcessing={() => setScreen("processing")}
                  onCompleteProcessing={() => setScreen("prompt")}
                  enhancementType={enhancementType}
                />
              </ScreenTransition>
            )}

            {screen === "processing" && <BurstModeLoadingScreen />}
          </AnimatePresence>
        </ScreenContentContainer>

        {screen === "prompt" && (
          <BackButton onClick={() => setScreen("modelSelection")} />
        )}
      </motion.div>
    </div>
  );
};

export default EnhancementWrapper;
