import React from "react";
import { Paintbrush } from "lucide-react";
import { StyleType } from "./types";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface StyleSelectorProps {
  style: StyleType;
  setStyle: React.Dispatch<React.SetStateAction<StyleType>>;
}

export default function StyleSelector({ style, setStyle }: StyleSelectorProps) {
  const styleOptions: { value: StyleType; label: string }[] = [
    { value: null, label: "None" },
    { value: "Cinematic", label: "Cinematic" },
    { value: "Animated", label: "Animated" },
    { value: "Digital Art", label: "Digital Art" },
    { value: "Photographic", label: "Photographic" },
    { value: "Fantasy art", label: "Fantasy art" },
    { value: "Neonpunk", label: "Neonpunk" },
    { value: "Enhance", label: "Enhance" },
    { value: "Comic book", label: "Comic book" },
    { value: "Lowpoly", label: "Lowpoly" },
    { value: "Line art", label: "Line art" },
  ];

  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <label className="text-white/90 text-sm font-medium flex items-center gap-1">
          <Paintbrush size={14} className="text-purple-400" />
          Style (Optional)
        </label>
      </div>
      <Select
        onValueChange={(value) => {
          setStyle(value as StyleType);
        }}
        value={style as string}
      >
        <SelectTrigger className="w-full h-10">
          <SelectValue placeholder="Style" />
        </SelectTrigger>
        <SelectContent>
          {styleOptions.map((option) => (
            <SelectItem key={option.label} value={option.value as string}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
