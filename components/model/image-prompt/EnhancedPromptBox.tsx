import React from "react";
import { Wand2 } from "lucide-react";
import { EnhancedPromptBoxProps } from "./types";

export default function EnhancedPromptBox({
  enhancedPrompt,
  useEnhancedPrompt,
  setUseEnhancedPrompt
}: EnhancedPromptBoxProps) {
  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <label className="text-white/90 text-sm font-medium flex items-center gap-1">
          <Wand2 size={14} className="text-purple-400" />
          Enhanced Prompt
        </label>
        <div className="flex items-center gap-2">
          <label className="text-white/70 text-xs flex items-center gap-1">
            <input
              type="checkbox"
              checked={useEnhancedPrompt}
              onChange={() => setUseEnhancedPrompt(!useEnhancedPrompt)}
              className="h-3 w-3 rounded accent-purple-500"
            />
            Use enhanced
          </label>
        </div>
      </div>
      <div
        className="
          w-full
          p-3
          bg-purple-500/10
          border border-purple-500/30
          text-white/80
          text-sm
          rounded-lg
          max-h-28
          overflow-y-auto
        "
      >
        {enhancedPrompt}
      </div>
    </div>
  );
}
