import React from "react";
import { Download } from "lucide-react";
import { ImageDisplayProps } from "./types";
import { downloadImage } from "@/utility/utility";


export default function ImageDisplay({
  isLoading,
  imageUrl,
  enhancementType,
  useEnhancedPrompt,
  enhancedPrompt
}: ImageDisplayProps) {
  return (
    <div className="w-full bg-black/30 p-6 flex flex-col items-center justify-center m-auto">
      {isLoading ? (
        <div className="flex flex-col items-center space-y-4">
          <div className="relative">
            <div className="animate-spin rounded-full h-16 w-16 border-2 border-purple-500/20"></div>
            <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-l-2 border-purple-500 absolute top-0 left-0"></div>
          </div>
          <p className="text-white/70">
            Creating your {enhancementType} image...
            {useEnhancedPrompt && enhancedPrompt && (
              <span className="block text-xs mt-1 text-purple-400/70">Using Enhanced prompt</span>
            )}
          </p>
        </div>
      ) : imageUrl ? (
        <div className="flex flex-col items-center">
          <div className="overflow-hidden rounded-lg shadow-2xl border border-white/10">
            <img
              src={imageUrl}
              alt="Generated"
              className="w-auto max-h-80 object-contain"
            />
          </div>

          <button
            onClick={() => downloadImage(imageUrl)}
            className="
              mt-6
              bg-black/60
              hover:bg-black/80
              text-white
              font-medium
              py-2 px-5
              rounded-lg
              transition-all
              duration-200
              shadow-lg
              flex items-center gap-2
              border border-white/10
            "
          >
            <Download size={18} />
            Download
          </button>
        </div>
      ) : (
        <div className="text-center space-y-3 max-w-xs">
          <div className="text-white text-lg opacity-60">
            {enhancementType === "headshot" && "Your portrait"}
            {enhancementType === "food" && "Your food image"}
            {enhancementType === "product" && "Your product image"}
          </div>
          <div className="text-white text-sm opacity-40">
            Select a prompt or create your own to generate
          </div>
          <div className="mt-4 text-purple-400/60 text-4xl">
            ✨
          </div>
        </div>
      )}
    </div>
  );
}
