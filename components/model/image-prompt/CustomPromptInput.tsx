import React from "react";
import { Wand2 } from "lucide-react";
import { CustomPromptInputProps } from "./types";
import { useToast } from "@/hooks/use-toast";
import { PREDEFINED_PROMPTS } from "@/lib/constants";

// Function to enhance prompts with Gemini
const enhancePromptWithGemini = async (prompt: string, type: string): Promise<string> => {
  try {
    const response = await fetch('/api/proxy/gemini', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        prompt,
        type
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || 'Failed to enhance prompt');
    }

    const data = await response.json();
    return data.enhancedPrompt;
  } catch (error) {
    console.error('Error enhancing prompt:', error);
    throw error;
  }
};

export default function CustomPromptInput({
  prompt,
  setPrompt,
  isEnhancing,
  setIsEnhancing,
  enhancementType,
  selectedPresetIndex,
  setSelectedPresetIndex,
  setEnhancedPrompt,
  setUseEnhancedPrompt
}: CustomPromptInputProps) {
  const { toast } = useToast();
  const promptOptions = PREDEFINED_PROMPTS[enhancementType as keyof typeof PREDEFINED_PROMPTS] || [];

  // Handle enhancing the prompt with Gemini
  const handleEnhancePrompt = async () => {
    if (!prompt.trim()) {
      toast({
        title: "Empty prompt",
        description: "Please enter a prompt to enhance.",
        duration: 3000,
      });
      return;
    }

    setIsEnhancing(true);
    try {
      const enhanced = await enhancePromptWithGemini(prompt, enhancementType);
      setEnhancedPrompt(enhanced);
      setUseEnhancedPrompt(true);
      toast({
        title: "Prompt enhanced!",
        description: "Your prompt has been enhanced with Gemini AI.",
        duration: 3000,
      });
    } catch (error) {
      console.error("Failed to enhance prompt:", error);
      toast({
        title: "Enhancement failed",
        description: "There was an error enhancing your prompt. Please try again.",
        variant: "destructive",
        duration: 3000,
      });
    } finally {
      setIsEnhancing(false);
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <label className="block text-white/90 text-sm font-medium">
          Describe your vision
        </label>
        <button
          onClick={handleEnhancePrompt}
          disabled={isEnhancing || !prompt.trim()}
          className={`
            flex items-center gap-1
            px-3 py-1.5
            text-xs font-medium
            rounded-lg
            transition-colors
            ${!prompt.trim() || isEnhancing
              ? 'bg-purple-500/20 text-white/40 cursor-not-allowed'
              : 'bg-purple-500/30 text-white hover:bg-purple-500/50 border border-purple-500/30'}
          `}
        >
          {isEnhancing ? (
            <>
              <div className="h-3 w-3 animate-spin rounded-full border-b-2 border-white"></div>
              <span>Enhancing...</span>
            </>
          ) : (
            <>
              <Wand2 size={12} />
              <span>Enhance Prompt</span>
            </>
          )}
        </button>
      </div>
      <textarea
        value={prompt}
        onChange={(e) => {
          setPrompt(e.target.value);
          if (e.target.value !== promptOptions[selectedPresetIndex || 0]) {
            setSelectedPresetIndex(null);
          }
          if (e.target.value === '') {
            setEnhancedPrompt(null);
            setUseEnhancedPrompt(false);
          }
        }}
        placeholder="Enter your creative prompt here..."
        className="
          w-full
          h-28
          bg-black/30
          text-white
          placeholder-gray-500
          rounded-lg
          p-4
          resize-none
          focus:outline-none
          border border-white/10
          transition-all
          duration-200
        "
      />
    </div>
  );
}
