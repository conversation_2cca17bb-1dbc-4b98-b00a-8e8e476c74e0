import React, { useState, useCallback } from "react";
import { Upload, X, Image as ImageIcon } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface UpscalingImageUploaderProps {
  inputImage: File | null;
  setInputImage: React.Dispatch<React.SetStateAction<File | null>>;
  inputImageUrl: string | null;
  setInputImageUrl: React.Dispatch<React.SetStateAction<string | null>>;
}

export default function UpscalingImageUploader({
  inputImage,
  setInputImage,
  inputImageUrl,
  setInputImageUrl,
}: UpscalingImageUploaderProps) {
  const { toast } = useToast();
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(false);

      const files = e.dataTransfer.files;
      if (files && files.length > 0) {
        handleImageUpload(files[0]);
      }
    },
    [setInputImage, setInputImageUrl]
  );

  const handleFileChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = e.target.files;
      if (files && files.length > 0) {
        handleImageUpload(files[0]);
      }
    },
    [setInputImage, setInputImageUrl]
  );

  const handleImageUpload = (file: File) => {
    // Check if file is an image
    if (!file.type.startsWith("image/")) {
      toast({
        title: "Invalid file type",
        description: "Please upload an image file (JPEG, PNG, etc.)",
        variant: "destructive",
      });
      return;
    }

    // Check file size (max 4.5MB)
    if (file.size > 4.5 * 1024 * 1024) {
      toast({
        title: "File too large",
        description: "Image must be less than 4.5MB",
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);

    // Create a URL for the image preview
    const imageUrl = URL.createObjectURL(file);

    // Set the image and URL
    setInputImage(file);
    setInputImageUrl(imageUrl);
    setIsUploading(false);
  };

  const removeImage = () => {
    if (inputImageUrl) {
      URL.revokeObjectURL(inputImageUrl);
    }
    setInputImage(null);
    setInputImageUrl(null);
  };

  return (
    <div
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
      className={`
        w-full
        h-64
        rounded-lg
        overflow-hidden
        border-2
        transition-all
        duration-200
        ${
          isDragging
            ? "border-purple-500 bg-purple-500/10"
            : inputImageUrl
            ? "border-white/20 bg-black/40"
            : "border-dashed border-white/20 bg-black/20 hover:bg-black/30 hover:border-white/30"
        }
      `}
    >
      {isUploading ? (
        <div className="w-full h-full flex items-center justify-center">
          <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
        </div>
      ) : inputImageUrl ? (
        <div className="relative w-full h-full group">
          <img
            src={inputImageUrl || undefined}
            alt="Reference"
            className="w-full h-full object-contain"
          />
          <div
            className="
              absolute
              inset-0
              bg-black/40
              opacity-0
              group-hover:opacity-100
              flex
              items-center
              justify-center
              transition-opacity
            "
          >
            <button
              type="button"
              onClick={removeImage}
              className="
                p-2
                rounded-full
                bg-red-500/80
                hover:bg-red-600
              "
            >
              <X size={20} className="text-white" />
            </button>
          </div>
        </div>
      ) : (
        <label className="w-full h-full flex flex-col items-center justify-center cursor-pointer">
          <div className="flex flex-col items-center">
            <div className="p-3 rounded-full bg-white/5 mb-3">
              <Upload size={24} className="text-white/60" />
            </div>
            <p className="text-white/60 text-sm mb-1">
              Drag & drop an image here
            </p>
            <p className="text-white/40 text-xs">or click to browse</p>
            <p className="text-white/40 text-xs mt-4">
              Max file size: 4.5MB
            </p>
          </div>
          <input
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            className="hidden"
          />
        </label>
      )}
    </div>
  );
}
