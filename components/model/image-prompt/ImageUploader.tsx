import React, { useState, useCallback } from "react";
import { X, Image as ImageIcon, Slide<PERSON> } from "lucide-react";
import { ImageUploaderProps, ControlnetType } from "./types";
import { useToast } from "@/hooks/use-toast";
import { usePathname } from "next/navigation";

export default function ImageUploader({
  inputImage,
  setInputImage,
  inputImageUrl,
  setInputImageUrl,
  controlnetType,
  setControlnetType,
  denoisingStrength,
  setDenoisingStrength,
  style,
  setStyle,
}: ImageUploaderProps) {
  const pathname = usePathname();
  const { toast } = useToast();
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  // Check if adding this file would exceed the size limit
  const checkImageSizeLimit = useCallback(
    (file: File) => {
      const MAX_SIZE_MB = 4.5;
      const fileSizeMB = file.size / (1024 * 1024);

      // Check individual file size
      if (fileSizeMB > MAX_SIZE_MB) {
        toast({
          title: "Image Exceeds Size Limit",
          description: `This image is ${fileSizeMB.toFixed(
            2
          )}MB. Maximum allowed is ${MAX_SIZE_MB}MB per image.`,
          duration: 4000,
        });
        return false;
      }

      return true;
    },
    [toast]
  );

  // Handle image changes (browse)
  const handleImageChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      try {
        setIsUploading(true);
        const file = e.target.files?.[0];
        if (file) {
          // Check file size limits
          if (!checkImageSizeLimit(file)) {
            setIsUploading(false);
            return;
          }

          // Create a URL for the image preview
          const imageUrl = URL.createObjectURL(file);
          setInputImageUrl(imageUrl);
          setInputImage(file);
        }
      } catch (error) {
        console.error("Error uploading image:", error);
        toast({
          title: "Upload Error",
          description:
            "There was an error uploading your image. Please try again.",
          duration: 3000,
        });
      } finally {
        setIsUploading(false);
      }
    },
    [checkImageSizeLimit, setInputImage, setInputImageUrl, toast]
  );

  // Drag-and-drop
  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      try {
        e.preventDefault();
        setIsDragging(false);
        setIsUploading(true);
        const file = e.dataTransfer.files[0];
        if (file && file.type.startsWith("image/")) {
          // Check file size limits
          if (!checkImageSizeLimit(file)) {
            setIsUploading(false);
            return;
          }

          // Create a URL for the image preview
          const imageUrl = URL.createObjectURL(file);
          setInputImageUrl(imageUrl);
          setInputImage(file);
        }
      } catch (error) {
        console.error("Error uploading image:", error);
        toast({
          title: "Upload Error",
          description:
            "There was an error uploading your image. Please try again.",
          duration: 3000,
        });
      } finally {
        setIsUploading(false);
      }
    },
    [checkImageSizeLimit, setInputImage, setInputImageUrl, toast]
  );

  const removeImage = useCallback(() => {
    if (inputImageUrl) {
      URL.revokeObjectURL(inputImageUrl);
    }
    setInputImage(null);
    setInputImageUrl(null);
  }, [inputImageUrl, setInputImage, setInputImageUrl]);

  const controlnetOptions: { value: ControlnetType; label: string }[] = [
    { value: "canny", label: "Canny Edge" },
    { value: "depth", label: "Depth" },
    { value: "pose", label: "Pose" },
    // Removed MLSD and Segroom as they are not supported on Flux
  ];

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <label className="block text-white/90 text-sm font-medium">
          Reference Image (Optional)
        </label>
        {inputImage && (
          <button
            onClick={removeImage}
            className="text-xs text-red-400 hover:text-red-300 flex items-center gap-1"
          >
            <X size={12} />
            Remove
          </button>
        )}
      </div>

      <div
        onDragEnter={() => setIsDragging(true)}
        onDragLeave={() => setIsDragging(false)}
        onDragOver={(e) => e.preventDefault()}
        onDrop={handleDrop}
        className={`
          w-full
          h-40
          rounded-lg
          overflow-hidden
          border
          transition-all
          duration-200
          ${
            isDragging
              ? "border-purple-500 bg-purple-500/10"
              : "border-white/10 bg-black/30"
          }
        `}
      >
        {isUploading ? (
          <div className="w-full h-full flex items-center justify-center">
            <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          </div>
        ) : inputImageUrl ? (
          <div className="relative w-full h-full group">
            <img
              src={inputImageUrl || undefined}
              alt="Reference"
              className="w-full h-full object-contain"
            />
            <div
              className="
                absolute
                inset-0
                bg-black/40
                opacity-0
                group-hover:opacity-100
                flex
                items-center
                justify-center
                transition-opacity
              "
            >
              <button
                type="button"
                onClick={removeImage}
                className="
                  p-2
                  rounded-full
                  bg-red-500/80
                  hover:bg-red-600
                "
              >
                <X size={20} className="text-white" />
              </button>
            </div>
          </div>
        ) : (
          <label className="w-full h-full flex flex-col items-center justify-center cursor-pointer">
            <div className="flex flex-col items-center space-y-2 text-white/60">
              <ImageIcon size={24} />
              <span className="text-sm">Drag & drop or click to upload</span>
              <span className="text-xs text-white/40">Max size: 4.5MB</span>
            </div>
            <input
              type="file"
              accept="image/*"
              onChange={handleImageChange}
              className="hidden"
              disabled={isUploading}
            />
          </label>
        )}
      </div>

      {inputImage && pathname !== "/model/avatar" && (
        <>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <label className="text-white/90 text-sm font-medium flex items-center gap-1">
                <Sliders size={14} className="text-purple-400" />
                Control Settings
              </label>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="block text-white/70 text-xs">
                  Control Type
                </label>
                <select
                  value={controlnetType as string}
                  onChange={(e) =>
                    setControlnetType(e.target.value as ControlnetType)
                  }
                  className="
                    w-full
                    bg-black/30
                    text-white
                    text-sm
                    rounded-lg
                    p-2
                    border border-white/10
                    focus:outline-none
                    focus:ring-1 focus:ring-purple-500
                  "
                >
                  {controlnetOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <label className="block text-white/70 text-xs">
                    Denoising Strength: {denoisingStrength.toFixed(1)}
                  </label>
                </div>
                <input
                  type="range"
                  min="0.1"
                  max="1.0"
                  step="0.1"
                  value={denoisingStrength}
                  onChange={(e) =>
                    setDenoisingStrength(parseFloat(e.target.value))
                  }
                  className="
                    w-full
                    accent-purple-500
                    bg-black/30
                    rounded-lg
                    h-2
                  "
                />
                <div className="flex justify-between text-xs text-white/40">
                  <span>More like reference</span>
                  <span>More like prompt</span>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
