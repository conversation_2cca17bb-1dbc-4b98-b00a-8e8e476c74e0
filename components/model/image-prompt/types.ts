import { Dispatch, SetStateAction } from "react";
import { InteriorDesignOptions } from "@/types/interiorDesign";

export interface ImagePromptProps {
  modelId: string; // The Astria tuneId
  userId: string; // Firebase user.uid
  title?: string; // The selected (or new) model title
  imageUrl?: string | null;
  videoUrl?: string | null;
  setImageUrl: React.Dispatch<React.SetStateAction<string | null>>;
  onStartProcessing: () => void;
  onCompleteProcessing: () => void;
  enhancementType: string;
  garmentModelId?: string; // Optional garment model ID for virtual try-on
  videoMode?: boolean; // Optional flag to enable video generation mode
  onVideoGenerated?: (videoUrl: string) => void; // Optional callback for video generation
}

export interface PresetPromptsProps {
  enhancementType: string;
  selectedPresetIndex: number | null;
  setSelectedPresetIndex: Dispatch<SetStateAction<number | null>>;
  setPrompt: Dispatch<SetStateAction<string>>;
  setEnhancedPrompt: Dispatch<SetStateAction<string | null>>;
  setUseEnhancedPrompt: Dispatch<SetStateAction<boolean>>;
}

export interface EnhancedPromptBoxProps {
  enhancedPrompt: string;
  useEnhancedPrompt: boolean;
  setUseEnhancedPrompt: Dispatch<SetStateAction<boolean>>;
}

export interface CustomPromptInputProps {
  prompt: string;
  setPrompt: Dispatch<SetStateAction<string>>;
  isEnhancing: boolean;
  setIsEnhancing: Dispatch<SetStateAction<boolean>>;
  enhancementType: string;
  selectedPresetIndex: number | null;
  setSelectedPresetIndex: Dispatch<SetStateAction<number | null>>;
  setEnhancedPrompt: Dispatch<SetStateAction<string | null>>;
  setUseEnhancedPrompt: Dispatch<SetStateAction<boolean>>;
}

export interface GenerateButtonProps {
  isLoading: boolean;
  setIsLoading: Dispatch<SetStateAction<boolean>>;
  prompt: string;
  // Removed negative prompt parameter as it's not supported on Flux
  enhancedPrompt: string | null;
  useEnhancedPrompt: boolean;
  isEnhancing: boolean;
  modelId: string;
  userId: string;
  enhancementType: string;
  setImageUrl: Dispatch<SetStateAction<string | null>>;
  onStartProcessing: () => void;
  onCompleteProcessing: () => void;
  inputImage: File | null;
  controlnetType: string | null;
  denoisingStrength: number;
  style: StyleType;
  garmentModelId?: string; // Optional garment model ID for virtual try-on
  interiorOptions?: InteriorDesignOptions; // Optional interior design options
  videoMode?: boolean; // Optional flag to enable video generation mode
  videoPrompt?: string; // Optional specific prompt for video generation
  videoModel?: "720p" | "480p"; // Optional video resolution model
  onVideoGenerated?: (videoUrl: string) => void; // Optional callback for video generation
}

export interface ImageDisplayProps {
  isLoading: boolean;
  imageUrl: string | null;
  enhancementType: string;
  useEnhancedPrompt: boolean;
  enhancedPrompt: string | null;
}

export interface ImageUploaderProps {
  inputImage: File | null;
  setInputImage: Dispatch<SetStateAction<File | null>>;
  inputImageUrl: string | null;
  setInputImageUrl: Dispatch<SetStateAction<string | null>>;
  controlnetType: string | null;
  setControlnetType: Dispatch<SetStateAction<string | null>>;
  denoisingStrength: number;
  setDenoisingStrength: Dispatch<SetStateAction<number>>;
  style: StyleType;
  setStyle: Dispatch<SetStateAction<StyleType>>;
}

export type ControlnetType =
  | "composition"
  | "reference"
  | "segroom"
  | "ipadapter"
  | "lineart"
  | "canny"
  | "depth"
  | "mlsd"
  | "hed"
  | "pose"
  | "tile"
  | "qr";

export type StyleType =
  | null
  | "Cinematic"
  | "Animated"
  | "Digital Art"
  | "Photographic"
  | "Fantasy art"
  | "Neonpunk"
  | "Enhance"
  | "Comic book"
  | "Lowpoly"
  | "Line art";
