import React from "react";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Info } from "lucide-react";

export interface UpscalingOptions {
  superResolution: boolean;
  hiresFixEnabled: boolean;
  tiledUpscale: boolean;
  tiledUpscaleVersion: 'v1' | 'v2';
  hiresCfgScale: number;
  resemblance: number;
  denoisingStrength: number;
  useModelTraining: boolean;
  modelId: string | null;
  focusPrompt: string;
}

interface UpscalingOptionsProps {
  options: UpscalingOptions;
  setOptions: React.Dispatch<React.SetStateAction<UpscalingOptions>>;
}

export default function UpscalingOptions({
  options,
  setOptions,
}: UpscalingOptionsProps) {
  const handleSuperResolutionChange = (checked: boolean) => {
    setOptions((prev) => ({
      ...prev,
      superResolution: checked,
    }));
  };

  const handleHiresFixChange = (checked: boolean) => {
    setOptions((prev) => ({
      ...prev,
      hiresFixEnabled: checked,
    }));
  };

  const handleTiledUpscaleChange = (checked: boolean) => {
    setOptions((prev) => ({
      ...prev,
      tiledUpscale: checked,
    }));
  };

  const handleTiledVersionChange = (version: 'v1' | 'v2') => {
    setOptions((prev) => ({
      ...prev,
      tiledUpscaleVersion: version,
    }));
  };

  const handleHiresCfgScaleChange = (value: number[]) => {
    setOptions((prev) => ({
      ...prev,
      hiresCfgScale: value[0],
    }));
  };

  const handleResemblanceChange = (value: number[]) => {
    setOptions((prev) => ({
      ...prev,
      resemblance: value[0],
    }));
  };

  const handleDenoisingStrengthChange = (value: number[]) => {
    setOptions((prev) => ({
      ...prev,
      denoisingStrength: value[0],
    }));
  };

  const handleModelTrainingChange = (checked: boolean) => {
    setOptions((prev) => ({
      ...prev,
      useModelTraining: checked,
    }));
  };

  const handleFocusPromptChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setOptions((prev) => ({
      ...prev,
      focusPrompt: e.target.value,
    }));
  };

  return (
    <div className="space-y-4 bg-black/20 p-4 rounded-lg border border-white/10">
      <h3 className="text-white/90 text-sm font-medium mb-3">Upscaling Options</h3>

      <div className="space-y-4">
        {/* Focus Prompt */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Label htmlFor="focus-prompt" className="text-white/80 text-sm">
              Focus Areas
            </Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info size={14} className="text-white/50 cursor-help" />
                </TooltipTrigger>
                <TooltipContent className="bg-amber-950/90 rounded-md border-amber-700/50">
                  <p className="w-60 text-xs">
                    Specify areas that need special attention during upscaling (e.g., "face details", "texture of fabric")
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <textarea
            id="focus-prompt"
            value={options.focusPrompt}
            onChange={handleFocusPromptChange}
            placeholder="Describe what areas need focus during upscaling..."
            className="w-full h-20 bg-black/30 border border-white/10 rounded-md p-2 text-sm text-white resize-none"
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Label htmlFor="super-resolution" className="text-white/80 text-sm">
              Super Resolution
            </Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info size={14} className="text-white/50 cursor-help" />
                </TooltipTrigger>
                <TooltipContent className="bg-amber-950/90 rounded-md border-amber-700/50">
                  <p className="w-60 text-xs">
                    Enhances image details and textures while increasing resolution
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <Switch
            id="super-resolution"
            checked={options.superResolution}
            onCheckedChange={handleSuperResolutionChange}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Label htmlFor="hires-fix" className="text-white/80 text-sm">
              HiRes Fix
            </Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info size={14} className="text-white/50 cursor-help" />
                </TooltipTrigger>
                <TooltipContent className="bg-amber-950/90 rounded-md border-amber-700/50">
                  <p className="w-60 text-xs">
                    Improves overall sharpness and clarity in high-resolution outputs
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <Switch
            id="hires-fix"
            checked={options.hiresFixEnabled}
            onCheckedChange={handleHiresFixChange}
          />
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Label htmlFor="tiled-upscale" className="text-white/80 text-sm">
              Tiled Upscale
            </Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info size={14} className="text-white/50 cursor-help" />
                </TooltipTrigger>
                <TooltipContent className="bg-amber-950/90 rounded-md border-amber-700/50">
                  <p className="w-60 text-xs">
                    Processes image in tiles to add details, improve skin tones, and give a more realistic look
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          <Switch
            id="tiled-upscale"
            checked={options.tiledUpscale}
            onCheckedChange={handleTiledUpscaleChange}
          />
        </div>

        {options.tiledUpscale && (
          <div className="ml-6 space-y-4">
            <div className="flex items-center gap-4">
              <Label className="text-white/80 text-sm">Version:</Label>
              <div className="flex gap-2">
                <button
                  onClick={() => handleTiledVersionChange('v1')}
                  className={`px-3 py-1 text-xs rounded-md ${
                    options.tiledUpscaleVersion === 'v1'
                      ? 'bg-purple-500/50 text-white'
                      : 'bg-black/30 text-white/60 hover:bg-black/40'
                  }`}
                >
                  Legacy (V1)
                </button>
                <button
                  onClick={() => handleTiledVersionChange('v2')}
                  className={`px-3 py-1 text-xs rounded-md ${
                    options.tiledUpscaleVersion === 'v2'
                      ? 'bg-purple-500/50 text-white'
                      : 'bg-black/30 text-white/60 hover:bg-black/40'
                  }`}
                >
                  V2 (Recommended)
                </button>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label className="text-white/80 text-sm">HiRes CFG Scale: {options.hiresCfgScale.toFixed(1)}</Label>
              </div>
              <Slider
                value={[options.hiresCfgScale]}
                min={1}
                max={10}
                step={0.1}
                onValueChange={handleHiresCfgScaleChange}
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label className="text-white/80 text-sm">Resemblance: {options.resemblance.toFixed(2)}</Label>
              </div>
              <Slider
                value={[options.resemblance]}
                min={0}
                max={1}
                step={0.01}
                onValueChange={handleResemblanceChange}
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Label className="text-white/80 text-sm">Denoising Strength: {options.denoisingStrength.toFixed(2)}</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Info size={14} className="text-white/50 cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent className="bg-amber-950/90 rounded-md border-amber-700/50">
                        <p className="w-60 text-xs">
                          Controls how much the AI modifies the original image. Must be between 0.1 and 1.0.
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
              <Slider
                value={[options.denoisingStrength]}
                min={0.1}
                max={1.0}
                step={0.01}
                onValueChange={handleDenoisingStrengthChange}
                className="w-full"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
