import PostCard from "./post-card"
import type { Post } from "@/types/blogs"

interface PostGridProps {
  posts: Post[]
  id?: string
}

export default function PostGrid({ posts, id }: PostGridProps) {
  return (
    <div id={id} className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mt-8" aria-label="Blog posts">
      {posts.map((post) => (
        <PostCard key={post.id} post={post} />
      ))}
    </div>
  )
}
