"use client";

import { PLANS } from "@/lib/constants";
import ProductCard from "./ProductCard";
import { useState } from "react";
import { motion } from "framer-motion";

export function PricingPlans() {
  const [isYearly, setIsYearly] = useState(false);

  return (
    <div className="max-w-7xl mx-auto">
      {/* Toggle Switch */}
      <div className="flex justify-center mb-6">
        <div
          className="relative flex items-center bg-gray-900 p-1 rounded-full cursor-pointer w-48 shadow-lg"
          onClick={() => setIsYearly(!isYearly)}
        >
          {/* Animated Background */}
          <motion.div
            style={{ willChange: "transform" }}
            className="absolute w-1/2 h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full transform-gpu"
            initial={{ x: isYearly ? "100%" : "0%" }}
            animate={{ x: isYearly ? "100%" : "0%" }}
            transition={{
              type: "tween",
              duration: 0.2,
              ease: "easeOut"
            }}
          />

          {/* Monthly Label */}
          <span className={`w-1/2 text-center text-sm font-bold z-10 ${!isYearly ? "text-white" : "text-gray-400"}`}>
            Monthly
          </span>

          {/* Yearly Label */}
          <span className={`w-1/2 text-center text-sm font-bold z-10 ${isYearly ? "text-white" : "text-gray-400"}`}>
            Yearly
          </span>
        </div>
      </div>

      {/* Pricing Plans Grid */}
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
        {PLANS.map((plan, index) => (
          <ProductCard key={index} plan={plan} isYearly={isYearly} />
        ))}
      </div>
    </div>
  );
}
