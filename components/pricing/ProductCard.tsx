"use client";
import React, { useState } from "react";
import { Button } from "@/components/ui/Button";
import {
  MoveRight,
  CheckCircle as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ader<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from "lucide-react";
import { useUser } from "@/context/UserContext";
import { createCheckoutSession } from "@/lib/stripe";
import { useToast } from "@/hooks/use-toast";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";

interface Plan {
  id: number;
  name: string;
  description: string;
  price: string;
  yearlyPriceInMonths: string;
  yearlyPrice: string;
  priceId: string;
  yearlyPriceId: string;
  credit: string;
  imageCredits: string;
  feature1: string;
  feature2: string;
  feature3: string;
  feature4: string;
  feature5: string;
}

interface ProductCardProps {
  plan: Plan;
  isYearly: boolean;
}

const FeatureItem = ({ children }: { children: React.ReactNode }) => {
  const isBestQuality = children === "💎 Best Quality";
  const isFluxModel = children === "Flux TM 1.1 photorealistic model";
  const isBestProcessing = children === "⚡ Best Processing";
  return (
    <motion.div
      style={{ willChange: "transform, opacity" }}
      className={`flex items-center space-x-3 p-2 rounded-lg border transition-all duration-300
        ${
          isBestQuality
            ? "border-2 bg-gradient-to-r from-yellow-400 via-pink-500 to-purple-600 "
            : isFluxModel
              ? "border-2 bg-gradient-to-r from-blue-700 via-cyan-700 to-indigo-500 "
              : isBestProcessing
                ? "border-2 bg-gradient-to-r from-yellow-400 to-yellow-700 font-bold "
                : "border border-primary/10 hover:border-primary/30 bg-white/5"
        }`}
      variants={{
        hidden: { opacity: 0, x: -20 },
        visible: {
          opacity: 1,
          x: 0,
          transition: {
            type: "tween",
            duration: 0.3,
            ease: "easeOut",
          },
        },
      }}
      whileHover={{ scale: 1.01 }}
    >
      <div className="transform-gpu">
        <Star className="h-5 w-5 text-primary/70 flex-shrink-0" />
      </div>
      <p
        className={`text-sm flex-grow ${isBestQuality || isFluxModel ? "font-bold text-white" : ""}`}
      >
        {children}
      </p>
    </motion.div>
  );
};

const ProductCard: React.FC<ProductCardProps> = ({ plan, isYearly }) => {
  const router = useRouter();
  const { user } = useUser();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const isPremium = plan.name.toLowerCase().includes("premium");

  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    if (user) {
      setIsLoading(true);
      createCheckoutSession(
        user.uid,
        isYearly ? plan.yearlyPriceId : plan.priceId
      ).catch(() => {
        setIsLoading(false);
        toast({
          title: "Error creating checkout session",
          description: "Please try again later.",
        });
      });
    } else {
      router.push("/login?next=/pricing");
      toast({
        title: "Uh oh! Something went wrong.",
        description: "You need to login first.",
      });
    }
  };

  return (
    <motion.div
      style={{ willChange: "transform, opacity" }}
      className={`container mx-auto px-4 py-8 lg:px-8 lg:py-16 flex flex-col justify-center rounded-xl border
        ${isPremium ? "border-primary shadow-lg shadow-primary/30" : "border-primary/20"} gap-4 relative overflow-hidden
        transition-all duration-300 ${isPremium ? "bg-gradient-to-b from-primary/10 to-background" : ""}`}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ scale: 1.01 }}
      transition={{
        duration: 0.2,
        type: "tween",
        ease: "easeOut",
      }}
    >
      {/* Badge for Most Popular */}
      {isPremium && (
        <div className="absolute top-4 right-4 bg-gradient-to-r from-slate-500 to-slate-900 text-white text-xs px-3 py-1 rounded-full font-bold uppercase tracking-wide shadow-md">
          Most Popular
        </div>
      )}

      {/* Plan name with sparkle effect */}
      <div className="flex items-center justify-center gap-2">
        <div className="transform-gpu">
          <Sparkles className="h-5 w-5 text-primary" />
        </div>
        <h3
          className={`font-bold text-lg bg-clip-text text-transparent bg-gradient-to-r ${
            isPremium
              ? "from-primary to-primary/60"
              : "from-gray-500 to-gray-400"
          }`}
        >
          {plan.name} Plan
        </h3>
      </div>

      {/* Price */}
      <p className="font-bold text-4xl text-center">
        ${isYearly ? plan.yearlyPriceInMonths : plan.price}
        <span className="text-sm">
          /{isYearly ? `month | $${plan.yearlyPrice}/year` : "month"}
        </span>
      </p>

      <p className="text-sm h-8 md:h-12 overflow-hidden text-center opacity-75">
        {plan.description}
      </p>

      {/* Subscribe button */}
      <Button
        variant="destructive"
        size="lg"
        className={`font-bold text-lg gap-1 mb-4 ${isPremium ? "bg-primary hover:bg-primary/90 text-white" : ""}`}
        onClick={handleClick}
        disabled={isLoading}
      >
        {isLoading ? (
          <>
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Processing</span>
          </>
        ) : (
          <>
            <span>Subscribe</span>
            <MoveRight />
          </>
        )}
      </Button>

      {/* Enhanced Features section */}
      <div className="space-y-3 mt-4">
        <div className="flex flex-col gap-2">
          <p className="flex flex-row items-center gap-2">
            <CircleCheckBig className="h-4" />
            {plan.credit} Credits
          </p>
          <p className="flex flex-row items-center gap-2">
            <CircleCheckBig className="h-4" />
            {plan.imageCredits} AI Images
          </p>
        </div>

        <div className="flex items-center justify-center gap-2 mb-3">
          <Sparkles className="h-5 w-5 text-primary/70" />
          <h4 className="text-md font-semibold text-primary/80">
            Plan Features
          </h4>
        </div>

        <motion.div
          className="space-y-2"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-50px" }}
          variants={{
            hidden: { opacity: 0 },
            visible: {
              opacity: 1,
              transition: {
                staggerChildren: 0.1,
                delayChildren: 0.2,
              },
            },
          }}
        >
          <FeatureItem>Flux TM 1.1 photorealistic model</FeatureItem>
          {Object.values(plan)
            .slice(10)
            .map((feature, index) => (
              <FeatureItem key={index}>{feature}</FeatureItem>
            ))}
        </motion.div>
      </div>
    </motion.div>
  );
};

export default ProductCard;
