"use client";

import React, { useEffect, useState } from "react";
import { User } from "firebase/auth";
import { useToast } from "@/hooks/use-toast";
import { EnhancementType } from "@/types/componentsProps";

interface AvatarModelSelectorProps {
  user: User;
  enhancementType: EnhancementType;
  onModelSelected: (modelId: string, modelTitle: string) => void;
  onTrainingStarted: () => void;
  onTrainingFailed: () => void;
}

const AvatarModelSelector: React.FC<AvatarModelSelectorProps> = ({
  user,
  enhancementType,
  onModelSelected,
  onTrainingStarted,
  onTrainingFailed,
}) => {
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  // For avatar generation, we're using a hardcoded model ID as specified
  useEffect(() => {
    const fetchAvatarModel = async () => {
      try {
        setLoading(true);
        
        // Fetch the avatar model from the API
        const response = await fetch('/api/proxy/avatar-models');
        
        if (!response.ok) {
          throw new Error('Failed to fetch avatar model');
        }
        
        const models = await response.json();
        
        if (models && models.length > 0) {
          // Automatically select the first model (there should only be one)
          const model = models[0];
          onModelSelected(model.id, model.title);
        } else {
          throw new Error('No avatar model found');
        }
      } catch (error) {
        console.error('Error fetching avatar model:', error);
        toast({
          title: "Error",
          description: "Failed to load avatar model. Please try again later.",
          variant: "destructive",
        });
        onTrainingFailed();
      } finally {
        setLoading(false);
      }
    };

    fetchAvatarModel();
  }, [onModelSelected, onTrainingFailed, toast]);

  return (
    <div className="w-full flex flex-col items-center justify-center px-4 py-16">
      <div className="text-center">
        {loading ? (
          <div className="flex flex-col items-center space-y-4">
            <div className="relative">
              <div className="animate-spin rounded-full h-16 w-16 border-2 border-purple-500/20"></div>
              <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-l-2 border-purple-500 absolute top-0 left-0"></div>
            </div>
            <p className="text-white/70">Loading avatar generator...</p>
          </div>
        ) : (
          <p className="text-white/70">
            Avatar generator is ready! You'll be redirected to the prompt screen shortly.
          </p>
        )}
      </div>
    </div>
  );
};

export default AvatarModelSelector;
