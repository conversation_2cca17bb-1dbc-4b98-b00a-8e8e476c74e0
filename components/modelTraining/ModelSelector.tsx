"use client";

import React, { useEffect, useState } from "react";
import { User } from "firebase/auth";
import { collection, query, where, getDocs } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { useToast } from "@/hooks/use-toast";
import { useUser } from "@/context/UserContext";
import ModelCreationForm from "./ModelCreationForm";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { EnhancementType } from "@/types/componentsProps";
import { usePathname } from "next/navigation";
import { ExsitigModelData } from "@/types/model";

interface ModelSelectorProps {
  user: User;
  enhancementType: EnhancementType;
  onModelSelected: (modelId: string, modelTitle: string) => void;
  onTrainingStarted: () => void;
  onTrainingFailed: () => void;
  modelType?: "lora" | "faceid";
}

const ModelSelector = React.memo<ModelSelectorProps>(
  ({
    user,
    enhancementType,
    onModelSelected,
    onTrainingStarted,
    onTrainingFailed,
    modelType = "lora",
  }) => {
    const [selectedModel, setSelectedModel] = useState("");
    const [existingModels, setExistingModels] = useState<ExsitigModelData[]>(
      []
    );
    const [loadingModels, setLoadingModels] = useState(false);
    const { credits } = useUser();
    const { toast } = useToast();

    const modelCreationContainerRef = React.useRef<HTMLDivElement | null>(null);

    // Fetch existing models from Firestore
    useEffect(() => {
      async function fetchModels() {
        try {
          setLoadingModels(true);

          const q = query(
            collection(db, "models"),
            where("userId", "==", user.uid),
            where("enhancementType", "==", enhancementType)
          );

          const snapshot = await getDocs(q);

          const models: ExsitigModelData[] = [];
          snapshot.forEach((doc) => {
            const data = doc.data() as Omit<ExsitigModelData, "id">;
            models.push({
              id: doc.id,
              userId: data.userId,
              title: data.title,
              tuneId: data.tuneId,
              modelType: data.modelType,
            });
          });

          // Filter models based on model type if specified
          const filteredModels = modelType
            ? models.filter(
                (model) => !model.modelType || model.modelType === modelType
              )
            : models;

          setExistingModels(filteredModels);
        } catch (error) {
          console.error("Error fetching models:", error);
        } finally {
          setLoadingModels(false);
        }
      }

      fetchModels();
    }, [user.uid, enhancementType, modelType]);

    // Handle model selection from dropdown
    const handleModelChange = (value: string) => {
      if (value === "__create_new__" && credits === 0) {
        // Show a toast instead of allowing the selection
        toast({
          title: "⚠️ Out of Credits!",
          description:
            "You have 0 credits left. Upgrade your plan to create a new model.",
          duration: 4000,
          variant: "destructive",
        });

        // Reset selection to prevent proceeding
        setSelectedModel("");
        return;
      }

      setSelectedModel(value);

      if (!value) return;

      if (value !== "__create_new__") {
        const foundModel = existingModels.find(
          (m) => String(m.tuneId) === value.toString()
        );
        if (foundModel) {
          onModelSelected(foundModel.tuneId, foundModel.title);
        }
      }
    };

    const pathname = usePathname();

    useEffect(() => {
      if (modelCreationContainerRef.current) {
        modelCreationContainerRef.current.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }
    }, [selectedModel]);

    return (
      <div
        className={`w-full flex flex-col items-center justify-center px-4 ${
          pathname.includes("virtual-try-on") ? "py-8" : "py-16"
        }`}
      >
        {/* SELECT / CREATE SECTION */}
        <div className="mb-6" ref={modelCreationContainerRef}>
          <label className="block text-sm font-medium mb-2 text-gray-700">
            {loadingModels
              ? "Loading models..."
              : "Select Existing Model or Create New:"}
          </label>
          <Select
            value={selectedModel}
            onValueChange={handleModelChange}
            disabled={loadingModels}
          >
            <SelectTrigger className="w-full  bg-white/10 backdrop-blur-sm border-gray-300 text-white">
              <SelectValue placeholder="Choose a model" />
            </SelectTrigger>
            <SelectContent className="bg-zinc-800 border-zinc-700">
              {/* Populate user's existing models */}
              {existingModels.length > 0 ? (
                existingModels.map((m) => (
                  <SelectItem
                    key={m.id}
                    value={m.tuneId}
                    className="text-white hover:bg-zinc-700 focus:bg-zinc-700"
                  >
                    {m.title}
                  </SelectItem>
                ))
              ) : (
                <SelectItem
                  value="No existing models"
                  className="text-zinc-400 italic"
                >
                  No existing models
                </SelectItem>
              )}

              {/* Separator */}
              {existingModels.length > 0 && (
                <div className="h-px bg-zinc-700 my-1 mx-1" />
              )}

              {/* Special "Create New" option */}
              <SelectItem
                value="__create_new__"
                className="text-purple-400 font-medium hover:bg-zinc-700 focus:bg-zinc-700"
              >
                Create New Model
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* IF THE USER WANTS TO CREATE A NEW MODEL */}
        {selectedModel === "__create_new__" && (
          <ModelCreationForm
            user={user}
            enhancementType={enhancementType}
            onModelSelected={onModelSelected}
            onTrainingStarted={onTrainingStarted}
            onTrainingFailed={onTrainingFailed}
            modelType={modelType}
            setExistingModels={setExistingModels}
          />
        )}
      </div>
    );
  }
);

export default ModelSelector;
