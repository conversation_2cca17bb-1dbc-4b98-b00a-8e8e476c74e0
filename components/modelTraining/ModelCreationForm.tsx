import React, { useState } from "react";
import { User } from "firebase/auth";
import { useToast } from "@/hooks/use-toast";
import { useUser } from "@/context/UserContext";
import { collection, doc, getDocs, updateDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { finetuneModelOnAstria } from "@/lib/api";
import ImageUploader from "./ImageUploader";
import { EnhancementType } from "@/types/componentsProps";
import { ExsitigModelData } from "@/types/model";
import FaceidClassname from "./FaceidClassname";

interface ModelCreationFormProps {
  user: User;
  enhancementType: EnhancementType;
  onModelSelected: (modelId: string, modelTitle: string) => void;
  onTrainingStarted: () => void;
  onTrainingFailed: () => void;
  modelType?: "lora" | "faceid";
  setExistingModels: React.Dispatch<React.SetStateAction<ExsitigModelData[]>>;
}

const ModelCreationForm = React.memo<ModelCreationFormProps>(
  ({
    user,
    enhancementType,
    onModelSelected,
    onTrainingStarted,
    onTrainingFailed,
    modelType = "lora",
    setExistingModels,
  }) => {
    const [title, setTitle] = useState("");
    const [className, setClassName] = useState("");
    const [images, setImages] = useState<(File | null)[]>([null, null, null]);
    const { decrementCredits } = useUser();
    const { toast } = useToast();

    const handleTrainModel = async () => {
      // Basic validation
      const validFiles = images.filter((f) => f !== null) as File[];
      if (!title.trim()) {
        toast({
          title: "Missing Title",
          description: "Please enter a title for your model.",
          duration: 3000,
        });
        return;
      }

      // Validate className if modelType is faceid
      if (modelType === "faceid" && !className.trim()) {
        toast({
          title: "Missing Class Name",
          description: "Please enter a class name for your faceid model.",
          duration: 3000,
        });
        return;
      }
      if (validFiles.length === 0) {
        toast({
          title: "No Images",
          description: "Please upload at least one image.",
          duration: 3000,
        });
        return;
      }

      // Check total image size before proceeding
      const MAX_SIZE_MB = 4.5;
      const totalSizeMB =
        validFiles.reduce((total, file) => total + file.size, 0) /
        (1024 * 1024);
      if (totalSizeMB > MAX_SIZE_MB) {
        toast({
          title: "Image Size Exceeds Limit",
          description: `Total image size is ${totalSizeMB.toFixed(
            2
          )}MB. Maximum allowed is ${MAX_SIZE_MB}MB.`,
          duration: 4000,
        });
        return;
      }

      if (validFiles.length < 4) {
        // Show warning but allow to proceed
        toast({
          title: "Recommendation",
          description:
            "For accurate results, we recommend uploading at least 4 images.",
          duration: 3000,
        });
        // Don't return, allow to proceed with training
      }

      // Model will be saved to Firestore via the callback API
      try {
        const subscribedPlanRef = collection(
          db,
          "users",
          user.uid,
          "subscribedPlan"
        );
        const querySnapshot = await getDocs(subscribedPlanRef);

        // if (querySnapshot.empty) {
        //   toast({
        //     title: "Subscription Issue",
        //     description:
        //       "No active subscription plan found. Please check your account settings.",
        //     variant: "destructive",
        //     duration: 5000,
        //   });
        //   return;
        // }

        onTrainingStarted(); // Show the "processing" screen

        // Fine-tune on Astria
        const result = await finetuneModelOnAstria({
          user: user.uid,
          title: title,
          images: validFiles,
          modelType: modelType,
          enhancementType: enhancementType,
          className: modelType === "faceid" ? className : undefined,
        });

        console.log("Result:", result);

        const bodyOptions = {
          tune: {
            created_at: result.data?.details.created_at,
            id: result.data?.tuneId,
            title: title,
          },
        };

        if (result.success) {
          const res = await fetch(
            `/api/callback/astria-tune?enhancementType=${enhancementType}&modelType=${modelType}&userId=${user.uid}`,
            {
              method: "POST",
              body: JSON.stringify(bodyOptions),
            }
          );
        }

        if (!result.success) {
          console.error("Error training model:", result.message);
          onTrainingFailed();
          return;
        }

        if (result.success) {
          toast({
            title: "Successfully Trained Model",
            description: result.message,
            variant: "default",
            duration: 5000,
            className: "bg-green-500 text-white",
          });
          onTrainingFailed();
          return;
        }

        const tuneId = result.data?.tuneId;
        if (!tuneId) {
          console.error("No tuneId returned from Astria!");
          onTrainingFailed();
          return;
        }

        // Get the first (and only) document ID in `subscribedPlan`
        const planDoc = querySnapshot.docs[0]; // Assumes only 1 plan per user
        const planDocId = planDoc.id;
        const credits = planDoc.data().planCredits;

        // Reference the correct document and decrement credits
        const planRef = doc(db, "users", user.uid, "subscribedPlan", planDocId);
        await updateDoc(planRef, {
          planCredits: credits - 1, // Decrease credits by 1
        });
        decrementCredits();
        // Tell the parent we have a new tuneId + the new title
        onModelSelected(tuneId, title);
      } catch (err) {
        console.error("Error saving model in Firestore:", err);
      }
    };

    return (
      <div className="w-full max-w-md bg-white/10 backdrop-blur-xl p-6 rounded-2xl shadow-md">
        <h2 className="text-center text-white mb-4 font-semibold">
          Upload Images to Train New Model
        </h2>
        <p className="text-muted-foreground text-sm text-center mb-4">
          NOTE: Creating a new model can take upto 8 to 10 minutes.
        </p>

        {/* Title */}
        <div>
          <label className="block text-white/80 text-sm mb-2 font-medium">
            Title
          </label>
          <input
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Enter title"
            className="
            w-full
            bg-black/40
            text-white
            placeholder-gray-400
            rounded-xl
            p-4
            focus:outline-none
            focus:ring-2
            focus:ring-purple-500/70
          "
          />
        </div>

        {/* Class Name - Only show for faceid model type */}
        {modelType === "faceid" && (
          <FaceidClassname className={className} setClassName={setClassName} />
        )}

        {/* Image Uploader Component */}
        <ImageUploader images={images} setImages={setImages} />

        {/* Train Model button */}
        <button
          onClick={handleTrainModel}
          className="
          w-full
          bg-purple-600
          hover:bg-purple-700
          text-white
          py-3
          px-6
          rounded-xl
          transition-all
          duration-300
          ease-in-out
        "
        >
          Train Model
        </button>
      </div>
    );
  }
);

export default ModelCreationForm;
