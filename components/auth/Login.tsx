"use client";

import Link from "next/link";
import { useState, Suspense } from "react";
import { Poppins } from "next/font/google";
import { cn } from "@/lib/utils";
import { login, signInWithGoogle } from "@/lib/firebase/index";
import { useRouter, useSearchParams } from "next/navigation";
import { User, sendEmailVerification } from "firebase/auth";
import { useToast } from "@/hooks/use-toast";
import { FcGoogle } from "react-icons/fc";

const font = Poppins({
  weight: "600",
  subsets: ["latin"],
});

// Component that uses useSearchParams - needs to be wrapped in Suspense
const LoginFormWithParams = () => {
  const searchParams = useSearchParams();
  const next = searchParams.get("next");

  return <LoginFormContent next={next} />;
};

// Main login form component
const LoginFormContent = ({ next }: { next: string | null }) => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [notVerified, setNotVerified] = useState<boolean>(false);
  const [user, setUser] = useState<any>(null);
  const router = useRouter();
  const { toast } = useToast();

  const resendVerificationEmail = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    if (!user) {
      console.log(user);
    }
    try {
      console.log(user);
      await sendEmailVerification(user);
      toast({
        title: "Resend Verification Successful",
        description: "Verification email sent. Check your email!",
      });
      setNotVerified(false);
    } catch (error: any) {
      setError(
        error.message
          ? error.message?.errors?.message
          : "Verification email failed"
      );
      console.log(error);
    }
  };
  // ✅ Handle Google Sign-In
  const handleGoogleSignIn = async () => {
    const response = await signInWithGoogle();

    if (!response.success) {
      toast({
        title: "Google Sign-In Failed",
        description: response.message,
      });
    } else {
      toast({
        title: "Sign-In Successful",
        description: "Welcome! Redirecting...",
      });
      if (next) {
        router.push(next);
      } else {
        router.push("/");
      }
    }
  };

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    const res = await login(email, password, rememberMe);
    if (!res.success) {
      toast({
        title: "Uh oh! Something went wrong.",
        description: res.message,
      });
    } else if (res.success && !res.verified) {
      toast({
        title: "Email not verified.",
        description: res.message,
      });
      setNotVerified(true);
      setUser(res.user);
    } else if (res.success && res.verified) {
      router.push("/"); // Redirect to a protected page
    }
  };

  return (
    <div className="w-full max-w-md p-8 space-y-6">
      <div className="flex flex-col items-center justify-center space-y-2 text-center">
        <h1 className={cn("text-3xl font-bold", font.className)}>
          Welcome Back
        </h1>
        <p className="text-sm text-muted-foreground">Sign in to your account</p>
      </div>

      <div className="p-6 bg-white/5 backdrop-blur-sm rounded-lg border border-primary/10 shadow-xl">
        {/* ✅ Google Sign-In Button */}
        <button
          onClick={handleGoogleSignIn}
          className="
            w-full flex items-center justify-center space-x-3
            py-3 px-6 border border-gray-300 bg-white text-gray-700
            rounded-lg shadow-md hover:bg-gray-100
            active:bg-gray-200 transition-all duration-300
            font-medium focus:outline-none focus:ring-2
            focus:ring-offset-2 focus:ring-gray-400
          "
        >
          <FcGoogle size={24} className="mr-2" />
          <span className="text-base">Continue with Google</span>
        </button>

        <div className="relative my-6">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-background text-muted-foreground">OR</span>
          </div>
        </div>

        {/* ✅ Email & Password Login */}
        <form onSubmit={onSubmit} className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-muted-foreground">
              Email
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 bg-secondary/50 border border-primary/10 rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50 transition"
              required
              placeholder="<EMAIL>"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-muted-foreground">
              Password
            </label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-3 py-2 bg-secondary/50 border border-primary/10 rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50 transition"
              required
              placeholder="••••••••"
            />
          </div>
          {error && <p className="text-sm text-red-500">{error}</p>}

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="remember"
                className="rounded border-primary/20"
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
              />
              <label
                htmlFor="remember"
                className="text-sm text-muted-foreground cursor-pointer"
              >
                Remember me
              </label>
            </div>
            <Link
              href="/forgot-password"
              className="text-sm text-primary hover:underline"
            >
              Forgot password?
            </Link>
          </div>

          <button
            type="submit"
            className="w-full py-2.5 px-4 bg-primary/10 text-white rounded-md hover:bg-primary/80 transition font-medium"
          >
            Sign In
          </button>
        </form>
      </div>

      <p className="text-center text-sm text-muted-foreground">
        Don't have an account?{" "}
        <Link
          href="/register"
          className="text-primary hover:underline font-medium"
        >
          Create an account
        </Link>
      </p>

      {notVerified && (
        <p
          className="text-center text-sm text-primary hover:underline cursor-pointer"
          onClick={(e) => resendVerificationEmail(e)}
        >
          Send verification email again?
        </p>
      )}
    </div>
  );
};

// Main component with Suspense wrapper
const LoginForm = () => {
  return (
    <Suspense fallback={<div className="w-full max-w-md p-8 space-y-6">
      <div className="flex flex-col items-center justify-center space-y-2 text-center">
        <h1 className={cn("text-3xl font-bold", font.className)}>
          Welcome Back
        </h1>
        <p className="text-sm text-muted-foreground">Loading...</p>
      </div>
    </div>}>
      <LoginFormWithParams />
    </Suspense>
  );
};

export default LoginForm;
