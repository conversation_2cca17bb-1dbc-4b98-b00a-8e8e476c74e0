"use client";

import React, { useState, useTransition } from "react";
import { Button } from "@/components/ui/Button";
import { useToast } from "@/hooks/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Lightbulb, Sparkles, ThumbsUp } from "lucide-react";
import { suggestAFeatureAction } from "@/actions/suggestAFeatureAction";

const SuggestFeature = () => {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [category, setCategory] = useState("");
  const [description, setDescription] = useState("");
  const [priority, setPriority] = useState("");
  const { toast } = useToast();

  const [isPendind, startTransition] = useTransition();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    startTransition(() => {
      suggestAFeatureAction({
        name,
        email,
        category,
        description,
        priority,
      })
        .then((res) => {
          toast({
            title: "Feature suggestion submitted!",
            description: res.message,
            duration: 5000,
            className: "bg-green-500 text-white",
          });
        })
        .catch((err) => {
          toast({
            title: "Error",
            description: err.message,
            variant: "destructive",
          });
        });
    });
  };

  return (
    <div className="w-full flex items-center flex-col justify-center px-4 py-16 lg:px-8 lg:py-24 gap-12">
      <div className="text-center space-y-4 max-w-3xl">
        <h1 className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
          Suggest a Feature
        </h1>
        <p className="text-lg text-muted-foreground">
          Have an idea to make Burst Mode even better? We'd love to hear it!
        </p>
        <p className="text-lg text-muted-foreground">
          Your suggestions help us prioritize new features and improvements.
        </p>
      </div>

      {/* Feature suggestion benefits */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full max-w-3xl">
        <div className="bg-secondary/5 backdrop-blur-sm rounded-lg p-6 border border-primary/10 flex flex-col items-center text-center">
          <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
            <Lightbulb className="h-6 w-6 text-primary" />
          </div>
          <h3 className="text-lg font-semibold mb-2">Share Your Ideas</h3>
          <p className="text-sm text-muted-foreground">
            Help shape the future of Burst Mode with your creative suggestions
          </p>
        </div>

        <div className="bg-secondary/5 backdrop-blur-sm rounded-lg p-3 sm:p-6 border border-primary/10 flex flex-col items-center text-center">
          <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
            <Sparkles className="h-6 w-6 text-primary" />
          </div>
          <h3 className="text-lg font-semibold mb-2">
            Enhance Your Experience
          </h3>
          <p className="text-sm text-muted-foreground">
            Get the features you need to make your AI photography workflow even
            better
          </p>
        </div>

        <div className="bg-secondary/5 backdrop-blur-sm rounded-lg p-6 border border-primary/10 flex flex-col items-center text-center">
          <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
            <ThumbsUp className="h-6 w-6 text-primary" />
          </div>
          <h3 className="text-lg font-semibold mb-2">Be Heard</h3>
          <p className="text-sm text-muted-foreground">
            We value your input and actively review all feature suggestions
          </p>
        </div>
      </div>

      <div className="w-full max-w-3xl bg-secondary/5 backdrop-blur-sm rounded-2xl p-4 md:p-8 border border-primary/10">
        <form onSubmit={handleSubmit} className="flex flex-col gap-6">
          <div className="flex flex-col sm:flex-row gap-6">
            <div className="flex flex-col gap-2 flex-1">
              <Label
                htmlFor="name"
                className="text-sm font-medium text-muted-foreground"
              >
                Name
              </Label>
              <input
                type="text"
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="w-full px-4 py-2 rounded-lg bg-secondary/10 border border-primary/20 focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Your name"
                required
              />
            </div>

            <div className="flex flex-col gap-2 flex-1">
              <Label
                htmlFor="email"
                className="text-sm font-medium text-muted-foreground"
              >
                Email
              </Label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full px-4 py-2 rounded-lg bg-secondary/10 border border-primary/20 focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Your email"
                required
              />
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-6">
            <div className="flex flex-col gap-2 flex-1">
              <Label
                htmlFor="category"
                className="text-sm font-medium text-muted-foreground"
              >
                Feature Category
              </Label>
              <Select value={category} onValueChange={setCategory} required>
                <SelectTrigger className="w-full px-4 py-2 rounded-lg bg-secondary/10 border border-primary/20 focus:outline-none focus:ring-2 focus:ring-primary">
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ui">User Interface</SelectItem>
                  <SelectItem value="generation">Image Generation</SelectItem>
                  <SelectItem value="enhancement">Image Enhancement</SelectItem>
                  <SelectItem value="video">Video Features</SelectItem>
                  <SelectItem value="integration">Integrations</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex flex-col gap-2 flex-1">
              <Label
                htmlFor="priority"
                className="text-sm font-medium text-muted-foreground"
              >
                Priority Level
              </Label>
              <Select value={priority} onValueChange={setPriority} required>
                <SelectTrigger className="w-full px-4 py-2 rounded-lg bg-secondary/10 border border-primary/20 focus:outline-none focus:ring-2 focus:ring-primary">
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Nice to Have</SelectItem>
                  <SelectItem value="medium">Important</SelectItem>
                  <SelectItem value="high">Critical</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex flex-col gap-2">
            <Label
              htmlFor="description"
              className="text-sm font-medium text-muted-foreground"
            >
              Feature Description
            </Label>
            <textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={6}
              className="w-full px-4 py-2 rounded-lg bg-secondary/10 border border-primary/20 focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Describe your feature idea in detail. What problem would it solve? How would it improve your experience?"
              required
            />
          </div>

          <Button
            type="submit"
            disabled={isPendind}
            className="text-xs  sm:text-sm md:text-lg bg-primary/80 hover:bg-primary/50 text-secondary py-3 px-6 rounded-lg font-medium transition-all duration-300"
          >
            {isPendind ? "Submitting..." : "Submit Feature Suggestion"}
          </Button>
        </form>
      </div>
    </div>
  );
};

export default SuggestFeature;
